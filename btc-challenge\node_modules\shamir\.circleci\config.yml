#
# Copyright © 2017 Co<PERSON> (<EMAIL>)
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

version: 2
jobs:
  build:
    docker:
      - image: maven:3-jdk-12-alpine
    working_directory: ~/repo
    environment:
      MAVEN_OPTS: -Xmx3200m
    steps:
      - checkout
      - restore_cache:
          keys:
          - v1-dependencies-{{ checksum ".circleci/config.yml" }}-{{ checksum "pom.xml" }}
          # fallback to using the latest cache if no exact match is found
          - v1-dependencies-
      - run: mvn clean verify
      - save_cache:
          paths:
            - ~/.m2
          key: v1-dependencies-{{ checksum ".circleci/config.yml" }}-{{ checksum "pom.xml" }}
      - run:
          name: Save test results
          command: |
            mkdir -p ~/junit/
            find . -type f -regex ".*/target/surefire-reports/.*xml" -exec cp {} ~/junit/ \;
          when: always
      - store_test_results:
          path: ~/junit
      - store_artifacts:
          path: ~/junit
      - persist_to_workspace:
          root: ~/repo
          paths:
            - src
            - target
            - package.json
            - package-lock.json

  testjs:
    docker:
      - image: oracle/graalvm-ce:latest
    working_directory: ~/repo
    steps:
      - attach_workspace:
          at: ~/repo
      - run: npm install tape --save-dev
      - run: npm test && npm run testwithjava
workflows:
  version: 2
  build_and_test:
    jobs:
      - build
      - testjs:
          requires:
            - build