<html>
  <head>
    <title>benchmark</title>
  </head>
  <body>
    <h1>See benchmark results in JavaScript console.</h1>

    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/benchmark@2.1.4/benchmark.min.js"></script>
    <script src="../secrets.min.js"></script>

    <script charset="utf-8">
      var suite = new Benchmark.Suite()

      suite
        .add("share and combine", function() {
          var key, shares, comb, newShare
          key = secrets.random(512)
          shares = secrets.share(key, 10, 5)
          comb = secrets.combine(shares)
          newShare = secrets.newShare(8, shares)
          comb = secrets.combine(shares.slice(1, 5).concat(newShare))
        })
        // .add('newPadLeft()', function() {
        //   secrets._newPadLeft(padding, 256);
        // })
        .on("cycle", function(event) {
          console.log(String(event.target))
        })
        .on("complete", function() {
          console.log("Fastest is " + this.filter("fastest").map("name"))
        })
        .run({ async: false })
    </script>
  </body>
</html>
