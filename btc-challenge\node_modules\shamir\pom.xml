<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright © 2017 Co<PERSON> (<EMAIL>)

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.codahale</groupId>
    <artifactId>common-pom</artifactId>
    <version>0.0.19</version>
  </parent>

  <artifactId>shamir</artifactId>
  <version>0.7.1-SNAPSHOT</version>
  <name>Shamir's Secret Sharing</name>
  <url>https://github.com/codahale/shamir</url>
  <description>
    An implementation of Shamir's Secret Sharing algorithm over GF(256).
  </description>

  <inceptionYear>2017</inceptionYear>
  <scm>
    <connection>scm:git:https://github.com/codahale/shamir.git</connection>
    <developerConnection>scm:git:https://github.com/codahale/shamir.git</developerConnection>
    <url>https://github.com/codahale/shamir</url>
    <tag>HEAD</tag>
  </scm>

  <dependencies>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <configuration>
            <archive>
              <manifestEntries>
                <Automatic-Module-Name>com.codahale.shamir</Automatic-Module-Name>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <!-- turn off added licences to contributed js files (and ./node_modules/**) -->
      <plugin>
        <groupId>com.mycila</groupId>
        <artifactId>license-maven-plugin</artifactId>
        <version>3.0</version>
        <configuration combine.self="override">
                  <header>com/mycila/maven/plugin/license/templates/APACHE-2.txt</header>
          <properties>
            <owner>Coda Hale</owner>
            <email><EMAIL></email>
          </properties>
          <mapping>
            <java>SLASHSTAR_STYLE</java>
          </mapping>
          <useDefaultExcludes />
          <excludes>
            <!-- Ignore license files. -->
            <exclude>LICENSE</exclude>
            <!-- Ignore text files. -->
            <exclude>**/*.txt</exclude>
            <exclude>**/*.js</exclude>
            <!-- Ignore all resource files. -->
            <exclude>src/main/resources/**</exclude>
            <exclude>src/test/resources/**</exclude>
            <exclude>node_modules/**</exclude>
          </excludes>
        </configuration>
      </plugin>
      </plugins>
  </build>

</project>
