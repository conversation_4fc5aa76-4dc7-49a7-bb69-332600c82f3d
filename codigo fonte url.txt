<!DOCTYPE html>
<html lang="en" >
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="format-detection" content="telephone=no">
    <meta data-n-head="true" name="keywords"
          content="bitcoin testnet faucet, blockchain, bitcoin testnet, litecoin, blockchain search engine, blockchain explorer, block explorer, bitcoin explorer,  litecoin explorer, blockchain analytics">


    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="/static/css/bootstrap.min.css" crossorigin="anonymous">
    <link href="/static/img/favicon.png" rel="shortcut icon">
    
        <meta id="theme-color" name="theme-color" content="#f1f1f1">
        <meta id="msapplication-navbutton-color" name="msapplication-navbutton-color" content="#f1f1f1">
        <meta id="apple-mobile-web-app-status-bar-style" name="apple-mobile-web-app-status-bar-style" content="#f1f1f1">

    
    
    <title>1 BTC challenge</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/static/css/style.css?1742507143360">
    <link rel="stylesheet" href="/static/css/font-awesome.css">
    
    <style>

        html[data-theme='dark'] {
            --bbfb: url(/static/img/bbfbb.svg);
        }
        html {
            --bbfb: url(/static/img/bbfb.svg);
        }
        ul {
            margin-left: 2em;
            margin-right: 2em;
        }
        ul li {
            margin-bottom: 0.9em;
        }

        p {
            font-size: 1.2em;
            text-align: justify;
            text-indent: 1em;

            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;

        }

        h5 {
            font-weight: lighter;
            font-size: 1.5em;
            font-family:   sans-serif;
        }

        h2 {
            font-weight: lighter;
            font-family:  sans-serif;
        }





    .textarea {

    text-align: justify;
    outline: none!important;
    min-height: 3.3em;
    -webkit-writing-mode: horizontal-tb !important;
    text-rendering: auto;
    letter-spacing: normal;
    word-spacing: normal;
    text-transform: none;

    text-shadow: none;
    display: inline-block;

    -webkit-appearance: textarea;

    -webkit-rtl-ordering: logical;

    cursor: text;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    margin: 0em;

    border-image: initial;
    box-sizing: border-box;
    direction: ltr;
    border: none;

    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;

    font-family: 'Roboto',arial,sans-serif;
    font-size: 1.2em;
    overflow: hidden;
    padding: 0.8em;
    padding-right: 2em;
    resize: none;


    box-shadow: var(--card-border-shadow);
    word-break: keep-all;
    background-color: var(--text-area-background);
    color:var(--body-color);
        border: var(--text-area-border);
    border-radius: 0.15em;
}






html {
    --text-area-background: rgb(247, 247, 247);
        --text-area-disabled-border: #bdbdbd;
        --text-area-disabled-background: #fafafa;
    --text-area-placeholder: #bbb;
    --text-area-border: 0;
    --x-btn-position: 0 0;
    --share-color: #7e57c2;
}

html[data-theme='dark'] {
            --text-area-disabled-background: transparent;
    --text-area-background: transparent;
        --text-area-placeholder: #2f434d;
--text-area-border: 0.05em solid #7a7a7a !important;
        --text-area-disabled-border: #232d36;
   --share-color: #ffc107;
        --x-btn-position: 100% 100%;
}





.wst  small{
    color: silver !important;

    padding-right: 0.2em;

}



.valid {
    color: #009586;
    position: relative;
    font-weight: bold;
}
.valid::after {
    display: block;
    position: absolute;
    color: #616161;
    z-index: 100;
    font-size: 0.4em;
    width: 100%;
    text-align: center;
    top: 2.9em;
    left:0;
}
.word-1::after {content: "1";}
.word-2::after {content: "2";}
.word-3::after {content: "3";}
.word-4::after {content: "4";}
.word-5::after {content: "5";}
.word-6::after {content: "6";}
.word-7::after {content: "7";}
.word-8::after {content: "8";}
.word-9::after {content: "9";}
.word-10::after {content: "10";}
.word-11::after {content: "11";}
.word-12::after {content: "12";}


.words-count-ok .valid::after {
    color:green;
}

.textarea span {
        display: inline-block;
    margin-bottom: 0.3em;

}

 .share-div .valid{ color: var(--share-color)!important; }
 .share-div .valid::after{ color: var(--share-color)!important; }







    </style>

</head>

<body>
<div class="content">
    
        <script src="/static/js/highcharts.js?1742507143360"></script>
        <div id="header-top" class="header header-top">
            <div class="container-fluid col-xl-11">
                <!-- Top fast access menu -->
                <div class="d-flex pt-2  flex-row justify-content-between align-items-start" id="top-menu">
                    <div class="btn-group ">
                        <div data-toggle="dropdown" role="navigation" aria-expanded="false">
                            <div>
                                <a class="pt-2 pb-3 d-flex flex-row justify-content-center" style="cursor:pointer;">
                                    
                                        <div class="bitcoin-testnet-logo"></div>
                                        <div class="ml-1 pt-2">
                                            <small>
                                                <span class="fa fa-lg fa-angle-down"></span>
                                            </small>
                                        </div>
                                    
                                </a>
                            </div>
                        </div>
                        <div class="dropdown-menu   " id="collapse-menu-net">

                            <a class="dropdown-item bitcoin-logo-menu" href="https://btc.bitaps.com">
                                <div class="bitcoin-logo-gray "></div>
                            </a>
                            <a class="dropdown-item litecoin-logo-menu" href="https://ltc.bitaps.com">
                                <div class="litecoin-logo-gray "></div>
                            </a>
<!--                            <a class="dropdown-item ethereum-logo-menu" href="https://eth.bitaps.com">-->
<!--                                <div class="ethereum-logo-gray "></div>-->
<!--                            </a>-->
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item  bitcoin-testnet-logo-menu  " href="https://tbtc.bitaps.com">
                                <div class="bitcoin-testnet-logo-gray bitcoin-testnet-logo-menu"></div>
                            </a>
                            <a class="dropdown-item  litecoin-testnet-logo-menu  " href="https://tltc.bitaps.com">
                                <div class="litecoin-testnet-logo-gray litecoin-testnet-logo-menu"></div>
                            </a>
<!--                            <a class="dropdown-item ethereum-testnet-logo-menu" href="https://teth.bitaps.com">-->
<!--                                <div class="ethereum-testnet-logo-gray ethereum-testnet-logo-menu"></div>-->
<!--                            </a>-->
                        </div>

                        <div class="top-stat"
                                
                                    
                             style="display: block;"
                                    
                                >

                            <div class=" d-flex pt-3   flex-row align-items-end ml-3" >
                                <h6 class="text-right pr-1 mb-0 pb-0"><b><span
                                        id="last_block_top">4 384 308</span></b></h6>
                                <small>
                                    <small class="top-stat-helper"
                                           id="tx-count-top">137</small>
                                </small>
                            </div>
                        </div>


                        <div class="top-stat"
                                
                                    
                             style="display: block;"
                                    
                                >

                        </div>
                    </div>

                    <div class="    d-none d-md-block   dashboard-layout fast-menu mr-4" id="fast-menu">
                        <ul class="list-inline">
                            <li class="list-inline-item"><a class="nav-link"
                                                            href="/blocks">Blocks</a></li>
                            <li class="list-inline-item"><a class="nav-link"
                                                            href="/tools">Tools</a></li>
                            <li class="list-inline-item"><a class="nav-link"
                                                            href="/statistics">Statistics</a>
                            </li>
                            <li class="list-inline-item"><a class="nav-link"
                                                            href="https://developer.bitaps.com">Developers</a>
                            </li>
                        </ul>
                    </div>

                </div>

                <!-- Dashboard -->
                
                    
                    
                        
                    
                    <div class="  d-flex flex-column flex-md-row   justify-content-around  dashboard "
                            
                         style="height: 0em;display: none;"
                            
                         id="net-dashboard">
                        <div class="   mb-3   d-flex flex-column    align-items-center dashboard-card "
                             id="last_block_header" style=" width: 100%; max-width:35em;min-width: 22.6em">
                            <div class="p-4  dashboard-layout  d-flex flex-column    align-items-center"
                                 style=" height:100%;">
                                <p class="lattolight" style=" ">Last block</p>
                                <h1 id="header_last_block_height"
                                    class="lattobold pl-1 pr-1">4 384 308</h1>
                                <p class="lattolight pb-1">Time from last block</p>
                                <div id="block_age">
                                    <div class="countdown-section"><b>00</b>
                                        <span>hours</span></div>
                                    <div class="countdown-section"><b>00</b>
                                        <span>minutes</span></div>
                                    <div class="countdown-section"><b>00</b>
                                        <span>seconds</span></div>
                                </div>
                                <div class="lattolight pb-1 pt-3 d-flex flex-row justify-content-around align-items-end w-100">
                                    <div class="d-flex flex-column align-items-center">
                                        <h5 id="unconfirmed_tx_count"
                                                
                                                    
                                                
                                            class=" pl-1 pr-1 bold d-block ">137</h5>
                                        <small>Pool transactions</small>
                                    </div>
                                    <div class="d-flex flex-column align-items-center">
                                        <div class="d-flex flex-row align-items-center">
                                            <span id="header_fee"
                                                  style="padding-bottom: 0.3em;"
                                                  class=" pl-1 pr-1 bold ">1</span>
                                            <div class="small">
                                                <small> ś<b>/vByte</b></small>
                                            </div>
                                        </div>
                                        <small>Best fee</small>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="d-flex flex-column   align-items-center dashboard-card  " id="market-dashboard"
                             style="width: 100%;">
                            <div class="p-2 d-flex ml-2 mr-4 pl-4 pr-4  flex-column   align-items-center    w-100"
                                 style=" height:100%;">

                                <div class="d-flex flex-row  justify-content-around  dashboard-layout  w-100"
                                     style="padding-top: 2em;padding-bottom: 2em;color:white;">
                                    <div class="w-100 tbtc-faucet">
                                        <div id="  w-100">

                                            <script>


                                                function validate(event) {
                                                    event.preventDefault();
                                                    let t = event.target.id;
                                                    let amount = Math.ceil(parseFloat($("#faucet-amount")[0].value) * 100000000);
                                                    let address = $("#faucet-address")[0].value;
                                                    let captcha = $("#faucet-captcha")[0].value;
                                                    let captcha_signature = $("#captcha_signature")[0].value;
                                                    let captcha_salt = $("#captcha_salt")[0].value;
                                                    let captcha_time = $("#captcha_time")[0].value;

                                                    if (address.length === 0) {
                                                        blink($("#faucet-address")[0], "#5A2121");
                                                        return;
                                                    }

                                                    if (!is_address_valid(address, true)) {
                                                        blink($("#faucet-address")[0], "#5A2121");
                                                        return;
                                                    }

                                                    if ((amount < 1000) || (amount > 1000000)) {
                                                        blink($("#faucet-amount")[0], "#5A2121");
                                                        return;
                                                    }
                                                    if (captcha.length !== 6) {
                                                        blink($("#faucet-captcha")[0], "#5A2121");
                                                        return;
                                                    }

                                                    $("#faucet-result").hide();
                                                    $(".tx_loading").show();
                                                    $("#receive").prop('disabled', true);
                                                    $("#dbs").prop('disabled', true);
                                                    async_faucet(address, amount, captcha,
                                                                 captcha_signature,
                                                                 captcha_salt, captcha_time);
                                                }

                                                function onload() {
                                                    var element = document.getElementById('receive');
                                                    element.onclick = validate;
                                                }



                                            </script>



                                            <div class="pb-3 ">Bitcoin testnet faucet</div>

                                            <div class="d-flex flex-column  align-content-center w-100">
                                                <div id="mt-3" class="w-100">
                                                    <form autocomplete="off">
                                                        <input autocomplete="false" name="hidden" type="text"
                                                               style="display:none;">
                                                        <input id="faucet-address" type="text" class="inbox-item "
                                                               name="address" placeholder="Your testnet3 address"
                                                               value=""
                                                               autocomplete="off" autocorrect="off" autocapitalize="off"
                                                               autocomplete="disabled"
                                                               spellcheck="false"
                                                               required="">
                                                    </form>

                                                </div>
                                                <div id="mt-3" class="w-100">
                                                    <div class="d-flex flex-row w-100 justify-content-end">


                                                        <input id="faucet-amount" class="inbox-item faucet-input"
                                                               name="address" placeholder="Amount"
                                                               value="0.01"
                                                               type="number"
                                                               step="0.00000001"
                                                               min="0.00000001"
                                                               max="0.01"
                                                               autocomplete="off" autocorrect="off" autocapitalize="off"
                                                               spellcheck="false"
                                                               oninput="" required="">
                                                        <div class="pl-3 d-flex flex-row align-content-end "
                                                             style="width: 60em;">
                                                            <span>tBTC</span>
                                                            <span class="ml-2" style="margin-top: 0em;"><small>Onetime limit 0.01 tBTC</small></span>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex flex-row">


                                                        <input id="faucet-captcha"
                                                               class="inbox-item faucet-input mt-4 mr-2 "
                                                               name="captcha"
                                                               autocomplete="off"
                                                               style="max-width: 9.5em;"

                                                               autocorrect="off" autocapitalize="off"
                                                               spellcheck="false"
                                                               oninput="" required="">
                                                        <div>
                                                            <div id="captcha-img" class="iqtest ml-3"
                                                                 onclick="refresh_captcha();"
                                                                 style="cursor:pointer;
                                                                         position:relative;
                                                                         background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAWgAAAB4CAIAAABQJv+tAADs30lEQVR4nOz9Z5SkeXkejD+Vc845h66qrs7d0zOzCwoLCkZIWEYCYwmOXgmJtASRBMgCY0mIIxBYgCwUbGNrBQYd2WAUQLLZnekcqyvnnHPO9f9wve7zfuX/eZ4PHGl3drr6qV+47+u+AikUCo3H49FotLe3l0gkzGbzaDS6vr6m0+lyubxcLi+Xy/39/XQ6LRaLk8nk1tbWcrlsNBq9Xm84HAqFwlwuZ7ValUplqVQqFAqr1cpkMo3H41arZbVaM5mMVqul0+mTyaRcLovFYhqNls1m2Wy2wWA4OztbW1trtVqlUsloNLJYLL/fLxaLDQZDoVAYDocUCmVtba1ardLp9Fqt1mq1HA4Hk8ns9/tsNhs/nc/nc7ncaDQqFAqTyaREIqHT6TqdjkQiDQaDRCKxXC4tFkuxWORyuTweLxaLabVaHo9XKpW4XC6JRBqPx3q9/vr6erlcTqfTjY2NXC7X7Xa1Wq1MJjs9PaVQKEKhcDgcyuXyWq3m9XpTqdRoNMKfXCwWIpHo7u5ue3s7l8splcpQKLS/vz8ajeLxuNPpvL29dTqdlUqlVqtpNJrZbEahUGazGZlM5nK5mUxGLBbbbLZCocBisRgMRq1WMxgM4XC42Wyy2eytrS2fz2e1WlutVq1W43K51WpVp9MNBoPRaKTT6YRC4Xw+v7m5OTw8XCwWs9ns7OzM4/EQBMHn81er1fX1NY/H6/f7+Ha4XG4ikWAwGNVqVSKRTKdTo9GYSqXsdnu73eZyuY1Go9FokEgkvV4vkUgmkwmdTicI4uLiQq1WNxoNk8kUDofX1tZyuRydThcKhVQqdTabFYtFlUpVr9fpdHq/3+fz+UqlcjabBQIBnU63Wq1IJBKdTm+1WjqdLpPJqFSqZrNZKBQUCgWTyaTRaI1GQygUEgSBL9ftdk8mk1wuZzAYzs/PdTpdp9Pp9XpUKtXpdDYaDaVSuVwu7+7uWCzWfD6fzWZarTaVShmNxuFwyGKxVqsVnU6PRCJ6vV6v11cqFQ6HMx6PWSwWQRCNRkMkEtVqtclkwuFwBAJBMplksVh6vb7VagmFwmKxyOfzmUwmmUxOpVI6nY7D4QyHw2QyiXeC34jFYtXrdZFINB6P6/W60WgUiUTFYlGj0XA4nMVi0el0xGIxiUQ6OTnBGs5mszQard/vL5fLXq9nt9sFAsF4PJ5MJolEAitzMplwuVwajSYWi9PptE6nw4uVyWSBQKDb7fJ4PBaLpVKpKBTKcDjsdrtcLrdcLjOZzHq9vr6+7vf7pVIph8PBAhgMBv1+X6FQzGYzhULR7/c7nU6hUCAIwmq19vt9i8VydXXl9XrH4/FqtQqHwy6XKxQKqdVqrVbbarVWq5VQKCRzOByCIHZ3dwOBwGAwyGQyrVZrZ2dnb29vtVrJZDI+nx8OhxUKBUEQk8mkVCrR6XSJRGKxWMRicbPZlMlk+Xz+8vJSqVRilSwWi3q9zuVymUym1Wrtdru9Xm+1WmFLNBqN4XCI5aXX69ls9mQyUalU1Wp1Op0aDAaJREKj0Ww2m06nWy6X4/EYx9BsNtvY2AiHw+VyebFYUKnUTCYjEolYLNbZ2Rmbzc5kMnw+v9lsajSak5OTYDDI4/E2Nzf5fH4kElGr1SQSSSwW2+32VquFpbZYLMbjca/XOzo62t7ettlsm5ubkUiERqMpFIrJZJJKpRQKhUwmo1AoOzs7Go1mOp1SqVQ+n+9wONrttsFgYLFY+XweS63b7fb7fYPBkM/nk8kkj8er1Wp7e3vtdns6nQqFwnq9ThCERqNZrVadTqfdbmu1WqlUWiwWWSwWjUYLBAIKheL6+hor22Qy9Xo9jUaTSqXG4/FyuVSr1RKJRK/XMxgMEom0Wq0ikUilUiGTyYvF4uzsrFqtYju1220ymRyNRlUq1XQ6ZbFYs9lMLpdfXFxQKBS9Xi8QCHq9nsFgIJPJw+GQRCKNRqObm5vVaiUSiSgUSr/fJ5FIsVisUqlEo1G5XN5qtebz+XA4ZDKZsVis2+3S6fRms8lgMGazGZ1Oj8ViLBarVquJxWKFQkEmk30+n0wmUyqVWq1WrVbX6/XlcplKpUgkEovFotPpZDLZZrM1m01sFaFQyGAwbm9vtVptMBgcDofz+fzq6mq1WuG/dTgcDocjlUpNJpNgMIgdrlQqWSwWhULpdrsej4dMJlssFq1Wu1gs5HL57u6uQCAIhULL5VIgENDp9Hw+X61WjUZjt9vFCYvTQSaTLRYLbMvVaiUWi1OpVCaTyeVyIpEoEolQKBQej8dmsxkMxmQycblcDAbDaDR6vV4Gg6FSqfR6PZlMxhEwmUxisdjt7W0sFsMadrlcPB4vEokIhcJer7e+vi6TychkslgsLhQKfD6/VCodHh5iIWF593q9brcrEAgkEgkOlFAo1O/3n3vuudlsZrPZut1us9nM5/NarVYkEjGZzMlkwufz7+7ueDxetVrl8/mDwaDVagkEArVanU6nG41GKBRarVa4/3C1TyaTxWLh9Xrr9XogEEilUrhdlErlZDIJBALlcnk+nweDQXKtVtPpdNls1mAwrK2tFQoFJpMZjUbr9bpCodDr9VKplEqltlqtRqMhkUjG4zE2baVS0el0Xq/X5XJZrdbFYjGdTiUSycbGRiwW02g01Wo1HA7X6/VOpzObzarVqkAgIAhiMBg4nU4s01KpVCwWBQLBYDAQiUSj0UgkEg0Gg263u1gsksmkSqXi8/n9fh9HSTgcFgqFEolEKBReX19zOJxms3l1dUWlUuVyuc1mG4/HarWaQqE4HI7FYlEqlUgk0mw2293drVars9ms0WhUKhWCIBgMxmq10ul04/HYZDK53e5SqUSj0SaTicViYTKZMpms2+1KpdLpdMrj8RQKRSaTqVQqFAoFPw7XVLlcLhaLnU5nNBqx2Ww2mz2fz9PpdLFYdLvdOp1usVj4/f5ut7u+vj4cDrVardVqvby8XFtbw1XPYDB4PN5yuczlcpVKhcvlDofD6XSaz+cdDkc+nxcIBMFgUCqVSqVSCoXS6/X0en00Gu10OgRBpFIpFosll8uVSmU4HOZyuTjmwuFwo9Egk8l6vb7f7wuFQoVCsbm5WS6XpVJps9lstVoSiWRtbY1Go+Vyud3dXb/fLxKJ6HR6tVolCALHBE7zZDIpFArxNtRq9Xg83tjY0Gg0qA4qlcrNzQ0uDJfLxeFwNjY2OBxOJBLJ5/NbW1uTyeT8/DyXyx0dHalUKmx+LpdbKpWWyyWJREomk0qlksPhsNlsDodDJpPNZvN0OuXz+bFYbLVara+vazQag8FAo9Gw8QaDAYlEwp43GAxSqZTH421sbFAolFgsRqFQrq+vLy4uRqNRNpsdDAYorlEscDgciURiMBhQFOAY7fV6qIaGwyEWRiqVSiaTuH7dbrdarRaJRDQardVqqVQquVzudDqPj4/ZbHYulzs9PSWTySQSqdPpqNVqsViMk3c6nW5vb8tkslQqdX5+vlwuI5FIr9eTy+W9Xi8QCJBIpK2trfl8TiaTZ7OZUqksl8sej8dms3G5XLlcvra2tlwupVKpz+e7vr6WSCQUCkUsFt/e3q6traEEY7FYOp1uMplEo1GtVmu325lMplarHY/HOzs70+k0nU5jK5HJ5MPDQ5VKhevNZDKhZiyXyywWC4WGXC632+3T6dRsNpvN5kKhMBgMCIIQCARcLnc6nZKJZ8+z59nz7PkhH7JcLhcIBK1Wa7lcBoNBkUg0nU4pFAqFQkkmk41Go1QqWa1WKpXa6XQEAoFWq43FYufn5xwOJxgMzufz+XzO4XDEYnG9Xr+5uUkmk9vb29Fo9MGDB1qtttvt4m6RSqXD4VCj0YxGo0ql0u/36/W6yWTK5/PZbJbH42k0GjKZHAgE1Gp1r9fLZDJut5vL5dbr9X6/z+Vy8VMWiwWDwRiPx06nUyqVzmYzPp+/sbGxWq3S6fT29vZyuSSTySwWSyaTCQSCeDyu0+kIglgsFu12G1Uci8VCaYP6P5vNjkYjEonE4/GoVCo6umazubGxIZFI8KtRqdTpdLparfb391H16fX6yWQiEonUavX29rZYLObz+SgfNjc3gXHQ6fRCoSCVSmUy2d3d3XK5LBaLgUAAQA8uH5lMtlqtNBqNXq9vt9t6vX6xWCgUCiqV2mw27wsuuVzOZDLtdjudTs/lcqPRSCqV7uzsbG9vWywWlJQUCkWr1d7d3QEV2trayuVyVCoV3TuFQhkMBmw2u9fricXieDw+Ho9DoVAqlUKX4Xa7K5WKXq9Hl1SpVHq9HkEQw+FwuVxyudxwOIwWBguj0WiMx2OZTMblcikUSrFYlEgko9GIwWCMRiOCICaTSaVSQQV6eHjIYDAeP37cbrfpdDr+AIvFGo1GJpMJ3X4qlWq32/P5fLVaDQYDdJHr6+uLxYLNZkulUpFIVK/X2+222WxWKpWDwWC1WqVSqVqtVq/XGQxGOp2ezWY7OztsNtvlcgGzQy+5ubm5u7tLo9HUanUmk5HL5dlsdjKZkEgkg8EwHo+r1SqLxbJYLFartV6vr1arfr+v1Wrj8fjGxsb19fVkMpnP56enp2w2O5FITKfTdrtttVojkchwONzZ2aHT6bPZbDweozhVqVQMBsNsNtfrdTSJeHVkMhlwj1qtRrUej8fT6bRAIEAX2Wg05vO5VCqt1WrFYpFOp6NL4nK5Tqfz6upKq9WazWYKhbJcLmezmU6nm06n920ymUwejUaDwUAsFqvV6larRaFQvF6vVCotlUoymezp06cUCiWXy8lksmKx2O/3q9Wq1+slk8kEQfR6Pb/f3+l0yGRyq9UqFosul0un0zGZTGwWmUxGLhaLhULBarXih41Go1KpZLPZ8vl8r9ebTCZsNrtYLI5GI6/XOxgM5vO5XC43mUw4EYLBIDqOarW6XC7ZbLbJZGIwGPiR0+m02WxWq9V0Oo1aq1AoSCQSqVS6XC4JgkDZqVQqC4UC3rhGo+l2u7VazWQyBYNBv9/P5/MfPXo0Go1yuZxGo7HZbMViUSwWdzodYATz+ZxGo83nc5Rk7XZ7OBxeXV1lMpnVarVYLJhMJmARBoMxGAxcLpdcLq/X6xwOp9/vazQatVoNMPXll19erVZoIsRicavVarVa9XrdbrejKtbpdD6fDw3w7e2twWCIxWIqlWo0GlWrVZ/Px+FwisUihULJZDLT6fT4+HhnZ0etVmNRMhgMhUKhVquxwigUCofDKRQKxWIRbb/D4YjH4xKJBF+bwWBQq9WLxeLBgwcnJyeZTGa5XGYymV6vx2AwisXi9fV1u91eLBb5fH59fV0qlcZiMaVS6XQ63W53t9vFni8UCmKxeDQaARJWKBQWi0WlUhUKBY/HQ6FQzGYzMBd8cpfLJZVKx+Pxq1/9ap/PNx6PtVptrVYDkIwzPZVKYQ+kUimn04kbqNvtAmxaLBbAAg4PDxUKBY/HOz4+ZjKZp6enIpEIfaJWq200GjQabTqdMpnM6XRqt9sVCkUul0PP1Ww2DQYDYMV0Oh0KhQaDgdForFaro9Go3W7b7XYU/PgbxGKxXC4XiURPnjyZz+e4YDweD7DkcDgciUT4fH6v12MymQCejUYjiUQik8mdTmdvb+/p06d+v79er6P1Pjg4aLVa29vb+NGj0Wg2m8lksmw2u7W1FY/Ha7WaVCq12+1ms7nX68Xj8UwmAzS0VqvF4/Fyuezz+WKxWLvdRhtLJpOpVKpEIkmn0/V6fTAYtNttiUQym81Wq5VarQZ8VqvVCoXCZDKp1Wo4DXHE1Ot1gUAwn8/z+bxKpcKhuVgsyGQymUzWaDRcLjcej9frdZw7w+FwMpksl0smk4kj5uLiYrVaKZVKLpe7WCxwQK+vr+O2AJCMpaJQKEgkUrFYJJFI0+m0XC6bzeZ2u02j0Uinp6cWi6XX6wGqWa1W+Xyey+V6PJ7T01MWi4VbvVKpeL1ezE3kcjmHw6FSqaPRCKeDxWLBnu/3+wwGI5vNmkymy8tLgUBgMBh6vV6lUmGz2QBTp9MpoNPRaKRWq+9RZa1Wq1Qqm80mj8dDdUChUKbT6WKxAJbe7/eHw6HZbF4ul9fX1/v7+1QqNZ1OVyqVg4ODfr/P4XCi0eja2lqlUgHgVCqVksmk3W7P5XLz+Vyr1SoUinQ6LRQKBQJBIBDg8/lmszmZTPZ6PbfbTaPRbm9vJ5PJ3t7eyy+/bLPZ6HR6MpkkCGI8Hh8eHrZarXK5vL6+DrhufX2dwWDE4/H74ch0OsU0QaVShUIhnU5XrVa3t7dvbm64XK5Go2m3271er16vOxyOdDrtdDpjsdjm5mY0GsViNRqNuAnpdDqbzV4sFtiucrm82WwuFgulUgmclclknpyceL1e1CalUonJZBIEgUrHbrcvl0s6nY6qMBaLcblcs9k8mUyGw2E8Ht/f3z87O8MM6+LiwmAwdLtdDodDIpFqtRpguU6nw+PxAInJZDKNRhMKhVQqVTqd3t3dzWazvV5PrVZzudxarUYQBJvNJggCm5/L5QKTw41Cp9PPz8/tdnutVmOz2Wq1GvUXj8fjcrm3t7eYNSyXS7yHaDTKZDLlcjl+ViAQ6PV6HA4H2CcmaDKZLJ1OKxSKZrOJvxaTFJRmeHUoV2k02uXlpdlsTqVSdDodKOZgMBgOhyKRKBgMMhgMr9fbbDZFIhGOEhKJdHZ2hkp5OBwaDAalUnlzc2O1WovFolarLRQKMpkMsCKdTm+320KhEOfdeDyu1Wputxvrtt1uWywWNpuNyh3Do0KhsLOzgzHiYrHodrs4UHAEAOGKRCI2my0ajbpcrtvbW1T3DAbD5XLd3NzYbLZyuVyr1chkMmq98Xg8m83m87lCoZBKpdgCbDZbJpOhY/D7/UAJq9UqCjfgKUdHR3t7ez6fz+VyARGPRqMMBmM6nT733HP4GGdnZ0ql0mAwTKdTUjAYVKlUsVhsPp9zuVwqlbpYLJxO593dHc7Xg4MDDOfsdnuv16NQKHw+//r6mkQiMZlMj8czGo36/f5oNALmhGmNXC4HQlssFslkMo1Gw2x1c3NzuVweHx/jZGWxWIVCAaU42gQ2m41Xho2E+ROfz8fEaDwe47httVomk4nFYiWTSQaDkUwmPR6P3+9/+PCh3+8fj8dGo7Fer8/ncx6P1+l0RCIRLrrBYIDzsd/vi8ViKpUK1BawNmBkFKLr6+uDwUAoFObzeY1Gk81mRSLRYrG43054V0DUksnk5uZmPB6fTCb4wjgcTqPR0Gq16XTa4/FcXV15PB4Gg9HtdqvVqlAonM1mOOzm8zlBEGQyGR9jNpul02kajSYQCNDmUKlUhUJRqVTwsWk0mtPpXCwWR0dHa2troVDIYrEsFotqtcrj8ex2+2AwwCexWq10Or1SqTSbzc3NzdvbW4/HQ6fTh8MhmUy+urp69OgRRhLpdLrb7bJYLJPJ1Ol0MITWarVMJjOTyaCoZjKZAoFgOp0mk0mLxVIoFFCKowC8vr7e2NgIBAKTycRkMrHZbFSLarU6HA4fHh5iNICqezgc9vt9qVSayWQajQa+ZVyn6BnJZLJIJNLr9aPRKJFIOJ1OtJMKhUKlUnU6HRaLNRwOy+WyxWKZz+d3d3cKhcJgMFxeXgKdDQaDGKlgIJ1IJPR6/fn5uUqlQs+LLkwmk8XjcYIgeDzecDhcLBZmsxmffD6f44DGPKXZbDocDj6fn8lkyuWySqWiUqn9fh/V+s3NzdbWVjab1Wg0vV4PFIfZbAbwOJ1Oo7xaLBYGg0EgEOTzeQwZ+/3+wcFBMBicTCYYlqO1TKVSuEf1ev10Oq1Wq6VSCeUzmUzG2FUoFOr1+qurq83NzWKxKJPJ2u22QCBA0VcoFA4PD6fTqd/vp9FoKCrL5TJWNWY0kUiEzWYrlUoymVytVmOxGJlM3tnZYTAY6L/IZLJWqy2XyzqdrlarcTgcpVJ5d3f3DBx99jx7nj0/9EPCuAj8LpyXarU6l8uRyWSFQoFZ44MHD25ubhwORzgc3t3dxQTYaDSCTMLj8e7RLIIgdnZ2QHfp9XrVanV3d3e1WoHK0Ww2J5MJlUqlUqkA1YxGY6VSwcHGYDAymczDhw/z+fxqtVoul8PhUKlU4kLr9/vAKZPJJFBAYHKZTAbllkwmG4/HXC63WCyC40ClUieTyeXlJTBCEonkcrlQKCaTSQzGMfxHH8HhcPR6fT6fJ5FIaAo0Gk06ndZoNAqFol6vo48Yj8fn5+fgy5HJZMAfgUBALBbncjlgb3Q6PR6Ps9lslUoFug54DdVq1Ww2Hx0dsVgst9s9Go1WqxWXy0WpDMxoPp+vr69ns1m1Wl0qldrtNnq64XCoUCgajYZcLkdf2mg0jEYjmUzu9/sEQQC3ns/nJpMJdyaPx5PJZKhoWCxWv9+v1Wp4LUqlMhaLTadTm82WTCadTieDwQAYBBgVlK16vQ5uxdra2vHxsUajwTDebDYPh0MAisCAANPM53Or1Vqr1arVqlqt1uv1qVQKwCeDwSAIgsFggCNXKpVAlwKcvFgsCIIYj8dkMnkymaAYlMvl5+fnmGsOBoPFYuFyufL5vEQiQVXS7Xbb7TbKWxqNNh6PB4PBdDoFdU0oFLbb7VKphOodvTCmpyQSicvlqlSqbrebSCTW1tby+TwIIwRBPHr06JVXXkGpgu6Jz+fTaLTRaFSv1z0eTyKR6Pf7RqNRKpW2Wi0ajVatVjkcDib6IECiZCgUCru7u5PJBD3LeDwWiUTpdNpqtbbbbTBryuUyRt0ikQgd+mKxyOVyFAplY2PD7/djStrpdICJ+v1+Eol0v55XqxVKCQBY29vbwFPBtxQKhZh1NBoNDoeTzWbBWMMSkkqlQD1DoZDD4Tg/P6fRaGw222q1djqdUqmk1WqxDc/OztBW83i8RqNBOj09xWycz+dHo1GbzXZ6erpcLg0GAxD+eDzO5/OlUmm32wV8BTrpbDYTiUSz2WwwGGA14N8mk0kmk9nr9bhcLkEQMpmMyWQOh0Mej4dqEC0Ml8tls9mVSiWfzwP+ZTAYwEecTudoNBoOhxaLBX3j8fExjUZDy4r/3d/fZzAYPp/P4/EEAgGDwTCbzUajERZZKBTyer3lcnk0GlGpVBKJhHkHBkaA1sBbQ1mo1WojkchisdDr9bVaDespn8+jrU2lUhwOJ5fLgZ9XrVYZDMZwOLTb7YlEgkQiabXa1WrF4XCm0+lkMplOp2w2OxaLyWQylUp1dXW1u7tbKpUEAgEopPieFouFVqsVi8WTyQQoI41GQ3F+dXW1s7PT7/crlYpEIikWiyaTiclkgl8bjUbRhgCua7fbRqOx0WgA8EM3NJlMcNaIRCKhULhYLGg0Wq1Wk0gkjUYDGwmt7NraGvoFj8ezXC6z2SyTyTSbzVQqFagt0FxgjclkEpQnn8/ndDovLy9Bymg2mxwOZzAY2O32TCZjNBr9fv9isRCLxTKZjM1md7vdbDaLdglUWh6PZ7FYbm5uRCIRUFVwsTCIAW0HW4hMJlcqlWKx+OjRo/Pzc5FIRKVSWSwWSDRcLrfT6aBpF4lEMpkMJM58Pk8QhEQicTgcZDL5yZMne3t73W43nU5LpVKhUIi2GpMBkClAI1oul6CWjEYjvHMmk8nhcORyOY1Gi8ViIpFIoVC02+3BYABarUKhSCaTgCRwsg+HQzA4XS7XcDjEiYnWDA1FsVh0OBzj8RgrE0w2iUTSbDbB42Cz2Wh1CYKoVCpSqdRgMPh8PovFks1mpVLparUik8nlchmggVarnUwmd3d3dru9UqnYbDYcZ7hm0OwIhUIcQKvVSq/XZ7NZNOw8Hi+fz/N4PGBSNBqNRqOhR+NwOLVabXNz8+bmxu12t9vtXC6nUChIAMAVCkUqlZJKpQKBIBqN8vn8bre7trYWDAYFAgHuKJFIVCqVTCZTJpOx2+2xWAx7DFRRwKgMBgNYVCQSEYvFlUoFLGYQQyuVCpVKtdvtp6enzz33HIlEyufzAKsEAgGNRtNqtWAuM5nMfD5fq9VAdGu325hubGxs3N7e7u7uzmYzIHlAjwF24PcHuZtCodBotMFgoNVqwai7vb0lkUgUCsXj8fh8PtzwwNuGwyEodODq9ft9FovF5/PFYvHTp08NBgOWDvi2uVwO09N8Pr+zs4PaikKh4Ix48OBBsVgEoNPpdMbjMY/H83q9rVYL8wi1Wi0UCkHp02g0NBoNDK7BYKBSqRQKxcnJidVqBUE4k8mAUOz3+x0OB4VCEQgEmLQJhcJOp5NIJGQy2XK5VKlUoOTd3t7OZjMWi4VXiiOYTqdzOByRSBSNRj0eT7/f7/V6s9kMQClAazKZPJ/PHzx4sFgssAQfPHgQDofn8zmKjslkAgVAsVgE2TydTrvdbhC9Z7MZvqBSqaTRaMC7u7i4WC6Xq9XKbDYD7b+fynU6nWazKZVKCYKYTCYCgWCxWOD1LpdLnU7HYDDy+Txgcj6fjxEYnU4HFRLgEZlMxr9is9nX19cul6tYLLZaLQ6HQ6FQ5vM54MNer2cymXDfikQiPp8/HA4rlQqdTpfJZOFwGPxrgUAA3IREIlmt1tvbW4lEwmAwLBZLLBabzWag2zebTaPRKBaLo9EoqNX5fB7lQ6PRkEqlmMoFAgGMpcEV1Gq1uVwOo/2NjQ1UFqlUCiQ04CkSiWQ4HK6vr4fDYXx3JBIJe2p7e/vy8tJoNOKu2tvbe/LkiV6vLxQKJBLJYrFgnsXn81utFuQgTqez3W6jcMN8sFgsGgyGWq02nU6xHkCG3NzcJJFIKCErlYrZbEb1JJPJLi8vJRKJ2Wy+vLzERBmjVRKAn9lsNp1OAd7odDqcdqBILhaLSqUCaAd1i1AoDAQC6HFA0CAIYj6fAxdMJBJMJtNgMHA4nLu7O5w7LBYrHo+vr6+DBo/a7+LiYjabmc1mEokEbvJoNAK65nK5UqkUhUIRiURomk5PT202m0wmOzk52d7eLpfL2Mnz+VwgEGQyGdzJQLx0Ot3p6anRaFwsFhgxYBxgsVgoFAqDwQBBE61Zu93GMWw2m4vFIhjKONHxfo+PjzFwBb9wOByC9oIKsNVq4UPK5XIceVATUCiUeDw+GAx2d3eBPPX7/UKh8OjRI4IgWq1WJpMZDofPP//8ycmJVqsFyQ8Q4MbGxg9+8AO9Xg8uII45/C6PHz9GCVOpVEDxYLFYVCoVQxOMeyuVCs4snU4XCoXkcjm422tra4PBIBaL4aPiwMVYGl9it9vFkHgwGLBYLBQ71WoV5SdBECaTqdVqgQ8uEAgsFguqbkxt1Go1WDAqlUoikTCZzHa7DaoulUoVCASgz1MolGAwyGKx2Gy2zWa7urpiMpkqlSqRSDx48ACLEJgxZAFCoVCj0YDnDr68Xq9HW5rJZOh0ularRTWHm69araJrRhlYKpVe9apXNZvNXC6HwxSiBIz2ut0uiguMnI6OjkgkErYKmUy+vr62WCyhUEgikRAEwWazx+MxZkYymYzD4QC7pVAo4A3j+gGJU6lUQp3E4/GwC+bzealUwnW4ublJEMTV1dVkMtnY2FgulxhR+/1+tVpNo9EAALfbbYfDgVYUNIrVakX838FZtVoFnqBWq5PJJA7oWq2G9k0qlaKoFIvF5+fnFApFp9PhiByPx6gJSCQSh8PJZDIOh2O1Ws1ms3a73Ww219bWUAbSaDQM9TEwwqrL5XLPwNFnz7Pn2fNDP6RGo4H6GYQ8oCloDcRiMQ45k8mUTqfNZrPP50MTOBqNAMIBZeRyuRh/tlotQEeYnFGp1GKxKBQKdTodxr0AUFC/yOVyMDg6nc5isTCZTChBG40GyKzQkgBlpNFoBEGMRqP19fUnT55APnCvLBiNRihAnE4n/jw0SyBlDofDUqlksVjQIS+XS7lcXiqV7HY7GisMXJPJpEAgAKoKKS06Q6/Xi4sFfVMmk4GCIBgM3rfljUZjfX0dUjeIQYrFImQdOLnH43Eul9ve3q7Vat1uVyaTxWKx3d3dfr8fCAQwNy0Wi5PJBPyITqcjl8sx8KPRaBCzarVacMCgMWk2m71eDyygwWCg0WhA3stkMuPxWKVSSaXS09PThw8fBoNBk8mE2w8j8FarpVar1Wr1zc0N2rfJZILZ/nK53NraIggC5QmFQgFNY3d39+bmxuv1QrAHfA7FFHgrFxcXHo+Hy+UOBgPM+RaLxcbGBkakJBIJ9TwWTKFQwBICBwm8DKPRCDYk8BS73X52doYerVarmc3mbreLlTObzTARDwQC6KnBDBQIBMvlMhwO8/l8yF5lMtlsNsNLI5FIgGAikchsNtvf3ycIIhKJOByOVquVzWbX1tbA8rRYLIAPxWLxkydPHA5HuVzGXLxarYLc1Gq1oOLxer2QWTIYDMj2+Hz++fk5Wm/00WAzOxyObrcLiorD4bi4uNBoNOVy2eFwABpbrVbgRmm1WvQpuVzOZDJhqrpYLKBWxzu8vr6WyWQQEy0Wi3K5TCaT8doVCsXt7a1KpeLxeOCktNtttNVutzsajarValBvZrMZ1MaYyxaLRbPZDN5DOp0GvAXZOr5ZnU4XjUbJACzAqnzw4EGz2ex0OjabbWtrCzrfer1+fX3NYDBarRbWnF6vdzgc+PI0Gg1weyqVinMnnU7L5XKVSkUQxGq14vF4GE1DJq9QKKA329jYwDhGqVS63W4QBCCsBuHPZrNNp9PRaIQvlUQiiUQipVL58ssvazSaQqFQq9XAVVutVm63W6/Xy2QyaE85HA6OpNFodHR0VK1WgcVAAQVVT6fTgQw5FAqVy+VYLIZDczQa0Wg0vGWwccCRx6+JuTqo8aBvQ+K5vr5eKBScTidQtFwu53A4IMEGw+J+1qPRaCAjhnKJzWZDCFcqlSaTidFoLJVKUqlUpVKJRKJOp1MulzOZzKtf/WpsufX1dS6X63A4qtVqIBBoNBrJZHI2mwErAQLqdrttNhukaJByAyEC1A+JPZvNbjabwWAQyvdKpRIMBkHfolAoiUTi9PQUL5ZMJoMBnUql2Gz2YDCgUqmJRAIYM+DPfr8fi8VAQo3FYnw+H60cKK3T6ZRMJvv9fg6HEwgEWq1WKBSC6A4dFkA+gFPj8fju7o5EIul0uvPzc5TTw+HQaDTWarVsNotJBA7HJ0+eyOXy6+vr4XB4c3PDZrNBfsFUhUQiORwOgUAAyiMWOb5QEon06NGjbDYbCAQ8Hg/4zWtra+l0GpdQqVTCB+52uzs7O+VyGTOR5XLpdDr5fD4oGEwmU6fTxWIxnOMA/tvtNoaA9Xo9k8lEo9HlcgnXiOPjYyqVura2RiKRut2u2WxuNpsEQWB/tdtt6CGVSqXP54OOsVargdbFZDJNJtPBwcHx8TGFQrm5uRkOh5lMBu02HC0qlYpYLC6VSlB4y2QyCCaLxSKIpGw2G9wT7FnwVg4PD2FGAcDo7u6uVqslEgmcFxArulwujUbjcDgkEonH4yGBRDgajbRarVAorFQqqDIwGcV9UigUBAIBQDiw34Ay9no93CF8Ph9rFzA14AMoXK6vr4FmR6PRg4ODSCQyn8+ZTCZKA3RT19fXkAYDGyeRSI1GY21tDeQrKpWqVCovLy8JgsAUEPWCwWC4urqCrAAgKPCkRCIB2ByEv/vSgEQiZbPZ3d3d6+tr8KlpNNpsNjOZTMA74UCBdz0cDjFpWywWm5ubV1dX4NLMZjO/369QKACVczgclUoFKEev1xME4fP5FovF3t5ePB7XarUgCAMsQMsKsBoOIFDr1Ov1YrEICwKoaZbLJXQ34OnivjIYDLe3t3K5HOhALBbD8Aucd6VSeXV1JRAIbDbbarUKhUJMJhMXlN1uxwQBSKdCoVgsFoVCAR4FSqVSLBYPh8NCoaBUKmk0GrbZaDSC6tdmszGZTAAEsVjMarUmEom9vb1isVir1ebzObi8WO7AtqlUaq/Xi8ViLpcLWA8UFhjA9/v9ZrMJJhia8Hw+P5vNnE4nQRBUKjUej9tstlKpBCeEYrGoVqtDoRCGQTqdDpgfvvRSqYR/KJFIMBzR6XSJRAIiJgg3yGQyLE6wYoVCYalUAh0Z/DTQ7cDvBlCN8TAcK2azGUEQGL0DOxuPxxhdA1bAGQFMp1qt4tsHdEImk2FWEgqFoB4qFovlchmTlO3tbfgesNlsMKpRhmNkC08GbENQLl0uF0EQmDqvVqtisWixWLA8MCeq1+tutzsSieA/BEkaS3E6nUJsheF6sViEZQnm7vl8HmJccKyLxeL6+nqlUhmNRqjQ8XphxxOPx8mRSAR6MIVCEY1GlUplo9HQ6/UPHz7s9Xr5fB5ILC5z8PwXi8Xd3V06nR4OhxwOp9frFQqFbDZ7dnYGlBujk1wuF4lElEolyiQmk4maRa/XV6vVVqtFp9NBHt/Y2MCKCYfDRqORSqU+99xzoB42m00Q9TAiAkJJEASkHNCVzedzCOFROcN9h0ql4muYzWZ2ux3mLphQYiIDjRBWCcrjjY2N7e3tarWKkgQMPI1GA1ujUChEJpNhxoGdJhKJ5HI5RqdQ9EP0gY8E1QCkcS+//HK1WgU8Nh6P19bWgPmbTKZsNqvX6z0ej9VqxYXJ4/FApp5Op61WiyAIGBGx2WwUzKVSCaI7lUpFo9Egw4cUGrgjflCv1+v1ehaL5e7ubj6fX15e4qpERQq5AM79SCSCxioUCgWDwWq1+uTJE1hyaDQanCkajabT6ZBIJBgXnZ2dVSoVt9vt8XhSqRSVSsXvAki7UCjEYrHFYoH+C0Qeg8Ewn88vLi4KhUK9XheLxdB65XI5j8dDpVKDwWA8HsfcHYNzOINgmmg2m202m81my2azGBLBLgD4Oq5Tr9dLIpHAXPB4PE6nk0aj5fN56BJQNMEAyWaz4S71+XwEQczn82g0ClIJhOfYyQqFArwMt9ttt9vhEjQYDECpYDAYJpMJsDHIFGg2e72eQCCA8gD87lwu9+DBg4ODA8xNWSzW1taWXC7P5/NXV1coS/Et22y2Wq0GbHs4HDIYjIuLC6wZmUwGayjcHBC2wMMJgoB2uw1ykFqtPjg4uLm5Qf/OYDBSqRQgCLvdbrPZCILo9/vxeLzdbm9vb8P+CmcZmKlSqZTJZGIi4/F4eDzezc0NrFWgiHkGjj57nj3Pnh/6IYGXmc/nKRQK2Jww5ptMJv1+X6VSZbNZgiAwj4FWjSCIzc1N9L0YaIEqCvYXQRBwZwKNB95wEAigGMMlLBQKIYGRSCS9Xs/lcvH5fAjv0AD3ej00txaLpVwuo3uUSCQikSiVSsnlcoy+BoMBLM8IgsAAXywWz+dziJqFQuFgMLi7u1utVtvb26h7W63W7u5uKBQikUhgOgKtvL6+3tnZgWiq0WiAWgpkgUajwfIP0ymBQABCUTQaBb0SSNLm5ma9Xk+lUpubm1QqNZlMgpuAGSd8sSQSCa4OmUyWSCSAksB8kE6ng8KL6h33nlarBWg3GAzC4TBMFW02G9wG5HL5YrEAa4ZMJoMMAh8a3JxgIqEBBFIoFotXq5VUKg2Hww6HA2xIfOOgDJ6fnwuFQpQ8i8UC35FarU4kEqgdmEwmVBXAC1CbgK4GVsi9/6BOp+Pz+fV6HdgnwKPFYoE+NBQKoSCFExeEGFAJw5cM7DhM2dG5QOx3fX09m80g+hiPx8VikcPhVKtVoN3VahUuBHCWOzs729jYAJ/i9vZ2fX394uICvGdYSYIfAYEcZtjxeBy+ZPjkUHZAaK/RaECYjsfj0ItD1y+TyWw222w2y2azSqXS7/fDnpLH4/l8PiaTaTQamUzmyy+/jOoMHgUgVt2XIVQq9erqamtrC84D2E0wVby7u5NKpXw+H++qXq+DwYRWAMJI+E6l0+mNjQ2UqyBb5/P5RqPh9XpXq1U8Hm80Gnw+X6PRAMKArgryLi6XC7c9sMVkMhl4sXK5/OnTpwBK7Xb7yckJFcKeRqPBYrFQjvr9/t3d3fF4DOcrkH8wCsHblEgkl5eXYrFYLBaDMLu2tgZDQCBAgKP39vZoNNrJyQngbnAojEYjhOFSqbRSqezu7jKZzGQyOZ1O4QbC4/E8Hs/19fX29jbYBPF4HEYeIpEI2s1ms1kulzc3N7vdLoVCwTJNp9NowPL5/Gg0Qvt6dHRkMBj29/cB3UM7i5MCnc50OoWL3HQ6BRoCa0zQxoCf4wwFmxgqePBc0UckEglY3dntdpSdDodjMBiAYAMDK5DHy+UymF34wrLZrFwuh3EGwDMQajEfiUQiZDJ5c3Pz6dOnTqczGo1C5tjv98FiUKvVhUIhEAgQBGE0GvP5/NraGpojqPim02mv1+PxeOCSwmcQJIXT09NSqQTpF3YjSM0wAYS6DEwTtH6pVIrJZGLVAjG5ublhsVjlcrnT6czncz6fD08GFosF1gadTsf/wj0km81ub29fXV0BEobBH3oHp9MJL8zlcok9MBgMQAgCGAG2vslkAnUIpDiCILBjgS7ncjm1Wg2lAvz+AHwAZe92u1DZwsGMy+VyudxUKgXEFJpGj8dTKBRAfsOMA+uZIIhisQiGNGzTIORfX19H+wy0mM1mg04NRJ/P58vl8svLy+Vyqdfr8X6ur68xagTsYjAYcCUMh0PQpvBmstksmFOA0oHZbW5u3iPxfr//4OAAoz1Ifq+urvR6vclkKhQKbrf75ubGYrHAZhETCVCi4TwA9l0ul8Olm8lkAHFOp9NKpQIJ/3PPPXd3d2c2m9lsts/ng2MOxNaxWMztdlMx8lGr1SqVKhKJ4NIGsQSyYiaT6XK5WCxWNBqFTwSDwcDXk0gkFAoFSEGgjV1cXEDVDohrMpnYbDaJRAKzs8vLSzqd7nA45vN5p9OB487l5SUqC41G02g0cGMAbRWJRNVqVa/XNxoN3JawCOl0OhApkkgkm802Go18Ph+6cTCpgYwKBAJQYMFhA2GcIIhgMNhsNjGfy2QySqVSKpXCfA0uNZVKRSAQFItFBoNBp9Ohd9br9eFwGPXF/RsEOY/BYMRiMYvFYjQac7lcsVi0Wq0EQXS7XVhXABWn0WiFQgHUYFDgpFKpxWJZrVbZbDYYDOIOAXxFoVCgQJdKpRBlYzTbaDSg41gsFjCCzufzwHSi0ahCoYDFodlshpii2+0mk0lw/PDH/H6/QCCAGAE2pQKBAL7NOGIUCgVmZ7lcbm9v7+LigsFg4Nfsdrt3d3dWq3VnZweYP5vN7nQ6V1dXdDp9fX0dboZyuZzP5xeLRYIgOp3Oo0ePUEuiu45Gow6HA1OAg4MDHJr/X8o5cHccKxAQUyiU1WoF3EGtVvv9/vX19UgkolAoYOnscDgA5Xi9XpFI1Gg02u12pVIBFovvBS+qUCg4HI5OpwPWHEBZqODpdDqQuE6nAzsVOIkAm8eJBnHGZDKBv0YsFrPb7Zubm3AtMRqNcCeGZ+Xh4WGz2RSLxShMtFotyLLZbNbtdoNIGgwGrVar1WrN5XJAYUulktPpxHVFpVJx0cJ0FnXfzs4OoJ9ms+nxeGq1mtVqbTQaMMcFOU0qld4rAOr1erPZVCgU8OyBoHxjY2MwGIA2BokzmK9er7fb7RYKBfh7EwTh8XhgrANaI7yjyUKhEBokQJhGoxG1WSAQgI0KBFoYf9brdVx6fD6/0WhgIQJZQcXx6NEjENcUCoXNZtPr9TQaDTvt5uaGz+ebTKbJZAJTTDqdnslkdnd3pVKpVqvFJjeZTHj1oJzf/1AqldrtdsPhMJ1Oh5NwrVbj8/ngR0Do1el0YF0plUrn8zkOewaDQaPRHjx4MJvNMFiBHACMchaLlclk9Hq9QqGAezsOzcFgMJvNoESo1+s2my0UCj1+/FggEOh0OpQqMGdVKBQwyIBmB8qu5XJZLpc1Gg2dTseE0mKx0Ol0WAFgTgTXLNgXFQoFjD9wU2HCAopqtVpF8Y+xBU7w6XR6fn6+vb2t1Wqfe+65aDSKE5nFYkE/zuFwAPKDEwGBNpVKLZfLbre72WyCko/5dyqVgtYO/Eg4CU0mE4PBgM3pdrspFAoWNO6uxWJhsVh0Oh06FxaLtb29nc/nQWYFdxas+Xq9fn5+3u12MUSkUChMJhMMV4fDcXR0dHl5iSFrOp1mMpnVahWT7EePHsGfCQOUy8vLm5ubWq2G6vr09LTT6Uwmk0ePHkEOYzabsdaxVAwGA/bAcDjEdrXZbKBUVioVkIaXy6XL5WIymVDiYET64MGDSCRiNptZLBYKYXQf1WoVuHIikSiVSvhqlstlIBCAw/hoNILbDWzlgsFgMBhMJBLFYjEUCkFlZ7Vas9ks3LegB8E5gtVLEIRWq3U6nbVajUajob3lcrmPHz8GF0Emk4EEjM4L3gi4SgeDAbwRwOOgUCjAWWk0mlQqtVqtOJEVCsXe3t5yuazVajc3N61Wy+VyofpmMBgHBweJRGKxWIACj67i7u4OsiMo5fr9vlqtfgaOPnuePc+eH/qhxmKxjY0Ni8WSSqUIgqjVahsbG+BNLhYLeB9jsKzVaqvVKsyUYL3p9/tVKtXa2trt7a1er8cE7uDgIBAIhMNh2H8BMxOLxbA2QZlAp9Nhf9JsNn0+H4IIAoGAVCqFpx7qUvTbGLnzeDwYLk2nU5QGGxsbrVYLNBXAtJDGg6kJpBAoLJ/PJwhCJpNhUIcSCb/gcDh0u913d3f9fn9vb+/eRws1PAJEgNTu7OzgeoFzDOS/EDgBVAapAUp8gBq4w6+urqRS6WKx8Hg8s9kMNqJCoRDWp5D5SyQSICyPHj1KJpO5XG5tbQ39CJ1O9/v9GCLCKgkopt1ubzabUB/AdQqVBTBUNCCtVsvtdsP+B1IFXDXwsKPRaBcXF3DrQQUUDAa9Xu9kMvH7/RsbG1BzqlQqGNVZrVaQBWAXCj4irAPwD0ejEXzSQ6EQ5HD3Il1U+OFw2Gq13jtToS/zeDwIKEDRUavVYH3+8OFD6OXAklSr1eCbwR0S2mvY9uPNgIQyn89BxAQ/ArkzxWLR6XR2Oh3YeXe73b29PVzLsVhsMpkAR5zP5/AWValUsFBDhQsFLUiTsALH987j8aRSKfgasNsBfo8EBtS8VCqVIAioePr9fi6Xg7iRIAiwy+Cnh3VbLpfRC9DpdPDiwFiJRqM8Hg9vCTaXUMFC4qhWq4EDDgaDx48fB4NBg8EA9U2lUnE4HLe3t5ubm/cpAr1eb7lctlot8Abi8TiSVqLRKEEQUN9SqVS8zMlkAqQDABP8BJlMJgmeqzabDd78y+VSLBabzeZ0Og2gjslkjkYj1HUAb4LB4GKxePnll4fD4fHxMY/HE4lEv//7v48uiM/nVyoVSH0Bl0DChO8JGjOwTQKBgEQiAWsFpoY8Hg8Md1D0ZrMZ+nONRlOr1QaDgcViiUajRqMxFArBUR44azgchs3H5uYmiC5QWwOlx5+B9RakwdPpFDkPUGfDjcLn84FthXkBvLNWq5VEIlGpVJlM5t43FNp2lUo1Ho8Brbnd7nQ6jb9zc3MTxp9CoRAWytlsFjg2yCAEQVCp1J2dnZOTE7fbDStd8C+5XC7mDp1OB/xfLpcL9BSmZ61WCyEJQEYEAgGSDbLZrNlsRqQWmUx++vQpDDgVCsXFxQXYTYPBALQrHL6DwQB8ZAyqRqPR1tYWhNjooQKBgEqlgqIPGsJwOAz0JJPJHBwcgHbdaDSoVCrsI2EFKJPJwCQE1Af6j8ViAStnc3MTW3E2m4EECJssABC4b/x+P5vNhgBPJpMlk0mEfgGrAlsEZrcMBgP8Y/ASQU7BaQVoH8QQTPHAkcEh8v+lPCFSo1wuZ7NZSAcQvgNPRrhywFAOlpdkMhmuq7gGkA2k0+lSqRSPx8OmBS0CLCeYJ4/H43A4jLgZTIswmgDgCkoUWi1oLCQSCWj4cKjBBMPhcDQaDVDRSSSSRqMBLjkYDJRKJahYo9EIpqcqlQo6RvjjAQ1ATA/eNgTTLpcLuU39fh/3qNPpxBBtNpvBMxxHXjAYhEc3GeE0EC8gJEWtVqMdgi8DLsZms4npKfgwCoWCzWaD7Y8b4Ctf+QqZTMblYzabwUTm/t8HBP5OpxOJRHCqgT8GQ7p7u9NsNgsZKGJpaDQaZj03NzcSiQSqvk6nc3NzgxuDSqWCEmqxWBwOh9FoXK1WoNZHIpFMJgNXEnByoRkHK45CoSiVyq2tLWDFl5eXR0dH2I1bW1tYlHCCkMvlsVgsGAxWKhWhUAgaEpDUXq8Hs3LYpqrVagaDAQcT6MSPj48JguByuRsbG/ekbHB7HQ4H2Kjw0et2u5izpNPparVqtVrBN/d4PIDrWSyWy+VKJpOI+YHNH/SyQFJKpRKGWfl8HqJSh8NRLBYBrCoUinuGD5I7kOWFYV4kEtne3gbtXa1WI6emWq2uVivQzFwuF51Oj0ajm5ubDAaDQqHAI4NEIoFWCK+KarXa6XQajcbJyQkK2nQ6bbPZ4IoIfSqTyby9vR0OhxcXFyDs0el0RH8BwBsOh+fn51tbW2Kx2Ov12u12DE2BZ/N4PHA3AW8ZjUaw2iC6l8vlJBJpZ2eHxWJB1z8YDAA5EwQBCTnk/7lcDn8Gvu3RaPT6+rpUKkE2jkIbQ6jRaCSRSPh8Pvxvms2m1Wp1Op0Il9PpdABW8K7K5TIqjkgkAl9egMowpsXoDTfczc3N2dkZJno4CsGsQ2UBByDYKSIPSalUrq+vy+Vy/JoYl4KICPbAdDrFQQDGAJvNhl3jcDgEi3d9fb3b7SK5qlKp+Hw+MNbw0w0GA7iX19fXsETCDWQwGKLRKLzvYXzNYrHUajXp3mjg/qq5NwqWyWSYtEskEoxLWCwWvKp6vd4rr7yCGB69Xo8q9wMf+MBrXvOap0+fovDG9BGphRhkwjcc3Yper08mk81mc3t7u9frYe8haAce64PBYG9vD9oT3CSotcD2pVKpCFXDiBsW56D04aKQSqUw4IFP1Gw2Ozw8PD4+Xq1WDx8+bLfbkI2xWCyr1QoHcxTker2+0+ngXOPz+S6XC6AsiOdutxs2/LVaDTMaOp2uUqmgx9dqtShw3G43UrMwh8ZxCdYpvHahbuj3+4lEAqr/ZDKp0WgAKPZ6PZQbo9HI5XKB9otfHzUgzIShWAc/JZFIiMViOp2ezWZ3dnYCgQAqTFT1l5eXwLmj0SgU5TjuoTED3kwmk+Elgw4Co+t0Ok0QBBIM4NkBsxkejwdHDziPaLVa0EZKpRISD1wuF5fLBZCJN0Mmk8fjMUEQCNwymUz3qZTwo4fdLopEhLaBizEYDMhkcq/Xe/jwYTQatVqtUO7d3NwgghPTEHCrp9MpXp3D4chkMhgSgV2azWYhpEAZi5E2JvfVahVdBljn8BCKx+NCoRAEc5zLarUa5V6z2URqXD6f73a7YHCAEuJ0OtFuLBaLZrOJuQGTyZRKpdhNCP0aDAYYrkFzAMEEvhSEWuEqhVUKrlKMS+E8jBQRzBlpNFqxWCyVSgcHB/f+HXQ6HSUDiUQym82ZTIYgCIFAIBKJUHqMx2PwmDc3N3u9HpjcAAfIZHI2m3U4HOinUJjj8sYcTaFQkEEHqtVqCoUim81iJg++EG4DoVB4dHSkUChwOtwbCL/1rW8tFAp7e3uYKfB4vO9+97u4RQUCQSQSodPpV1dXsANBwdnr9TDv4XA4cDqg0+nX19eIuiSTyTgXoWrd3d29u7vL5/NwfIG7N0xih8NhvV6HeKFerxsMBgS7AdZ2u92DwaBcLoP2Y7FYIFvI5XIWi+XRo0enp6ehUAgEB3Q0hULh/Pw8kUgQBAFhFZfL3d/fx8EHigfIUSBHYM4yGAzMZjMQGdjG53I56GIgiMCWgxkfbHgNBoNer7+9vQWUjZQDAPhIb0Vzi+7U6XQi6VYoFMIGye/3w4gYUR1YJfF4/Pb2ls/nA77h8/nlchmeZv/9v//3t7/97e9+97u/8pWvKBSKJ0+ejMdjrAD4oWcymVKpBASkUqm4XC6j0Xh3d1cqlWA5M51OYasFKtFgMAD9hEaj9Xo9THnB3EFRg/8XCoCTkxO4QhAEAecUHDdgGKJHJggCeBboLTh90BSLRKLVaoWlLBQKPR4PCHsYVK9WKwhhwc4eDAZXV1cOh4NKpYpEIiA1sMYhCGIymbzyyitKpXJnZwfdIo4n2MfDNxjgCNznYRSI7wjmSUArRqPR7e0t2vlIJFKtVmUymV6vx1zJ5/Op1WoALsFgEC7hyLWB0sxgMJRKJegk4fwslUq5XC5kcufn57e3tyjYz87OfvVXfxUVOhwAkeaJPXxxcaHT6TDPxjCl1WqB2BYOhyuVSqVSQakF1hkqU7vdPp/P0ZACZUPg6fX1NYxL7mViqDFhYc9kMtPpdCaTUSgU3W7X6/UqlcpAIPBsqvLsefY8e37ohxQOh/V6vd/vh34MqAnKDVAVF4sF0ARYWk4mE9CiNBrNl770pVAoJBaLX//613/iE58QCoV0Ov23fuu3oFDEWQ7oFBZDBEFEo1FID81mM/45jOfcbjfch4RC4c3NDbgMMpkMXgk///M/D4rEfD7/9//+34PfBa4X9I4WiyWXyxEEYTQakTUN30cAJWDs2O32u7s7ZEQTBAGPBkTbHh4exmIx0OpVKhVQqGw2y+FwQDZHNCyuZUDNEonkve99r1arnc1mX/rSl4LBIKhioJCBEQBqABIxINMKh8NwVFWr1WdnZ/cVPjyp4J0fCAS8Xu/p6SmDwUDxbDQaA4EAvPD9fn+v19va2oKiFEonqOalUmmj0dja2up0OqlUSiAQHB0dPXnyhM/n+/3+//Af/gMwbw6Hg3Q7vLd6vY6wIgaDAXolitDFYmE0Gm9vb4HIXF5eIncWuU1olVutFhLSer1eNpsFaKXT6ahUaqPRgNkXDIfhRmEwGJATjoFUKBSiUqlWqxUjqnA4bDKZisUi3G0RB0ulUjFbgW8ItAWwTYD3Fwqf5XKJ+RpWQqfT2dnZabVaYNDgDyBQCqwij8dzfn6+vr5OEAQ6/9PT0729PYAUoOG+/PLLYLWjjH/Pe94zmUx+/ud/3mq1yuVy+LzCoGwymQAVRmAwWstUKgUnF+Du0+kUUCW6HpfLhTFKLpdzuVxoCZEuOB6PhULhV7/6VTgnVqtV4Mpf+MIXotHozs4OmUxGHjP+E7/fv7W1heQHyL6FQiFMnrlcrkAgAMUWXCe0P/c/C11MpVJRKpWwaMzn8263G1aM4Oao1erb29vVagVHUpBZSACN8TsoFAp4T+Mtw7METjyQTqNQRDxHp9P5/Oc/3+l0DAYDDES/9KUvSaXSn/qpn9rc3ByPx7D5gNQFuDp8K8lkMsJ4ENRkNBozmYxQKCSRSFC7QC8A748/+7M/A8sYe+y1r33to0ePwuEw5i+AM9FG7u3thcNhTBaheoC5A+Sha2trrVYLykIkdMHLD9qHUqm0vr4OADUUCiFxx+VywVoCYg2A81jT4Oe+//3vh5fiJz/5SVCY7x2ux+MxOGwikajVaoGDjBWGNgqHAuwhMpkM+K+wqEF6BX4iMsowL1xfX59OpwKBABA1Qu2BjWHKgM0JC2h898Ph8C1veYtSqYTfz0c/+lE0UPD4MBgMEonk5uZmOp0ibQs4HAwyS6USlUpFpBAMgQAJwciTTqcHAoH5fK5WqyUSSb/fR4IhTDGpVCp8UqbTKd4b4AyJRFIqlSqVisFgQJwi3oBUKg0EAlB8ajQaiPcLhQKMPNB8eb3ecDi8sbFxenoKXAATJfj0MZlMEMYBLUGhD3k3QRCNRmN7exsfcnd39/z8HNYzKMtxCQFZiEQiyEwyGAwgKMI1ttfrPX36NJvNPnz40Gq1wgMJkV1grEUiEaPRyOPxTk5OYKrs9XovLi4QBj6bzaDEhcu5RCKpVCq4gHk8nlgsPj09hWESrCdHo9E3vvGN4+NjZJvCsO9+yrlarT72sY/BpAaxQZi5bGxsoD1ks9kwbYIBPahlPB4PE9ZWq8Xj8TB/QcQ6jUZD1iJkKXB4WV9fR0wcoqeAWkA+Uq/XydVqNZVK3d3dAfIkCOLm5iYYDEIwjkhRvDhMsMvlMjxscZ7N53N8W1Qq9b3vfW+j0fjyl7/8jne8A6a7R0dHIPOSyeRCoYDZFcx+EG9FIpHu/ezB0UDk1Gg0+uY3v/mBD3yg0WiEw2F8PZPJ5D5k3O12Hx4ebm1tKRQKuVy+v78PLXOxWLy5uclkMlDZwQgbRr4AihkMxs7OTjablUgkADihaAqHw6lUCksZgzpMjgUCAf5aWLQjsA7RdnB5WCwWT58+jcfjMIuHHU4ul/P7/UajERRpHDeJRAIMX/h6AwIEggscFIYdyArCbBs2/FarFaPN29vbRqORy+VyuRy8+eHvgi0HdAC2ToPBAAYwVqsV5yzYGfB26/V6VqtVoVDgNIfvCbKO5vO50WiUSCQ7Ozv3wZfX19dyuZwgCHg7YYvCYx1s9Lu7O+zeWCwGEjpkYAhhgtM/LL96vd7m5mYmk/nbv/1bsCfAQRCLxVBbjEYjkCZgngbWD8oHi8WCKXU+nwfjBlIRSO+wQkD2h44hn89PJpNms3l4eIgSbD6fJ5NJOGgxmUzUvMAyMYEGXsbj8aDcvy8DLRaLQCBot9tIP0okEihbhEIh5qMulyscDiN6KplMqlSqZDKJGRw8XMDxefTokVKp7Ha7fD5fIpGgoKjValtbW2w2G0M0DC+kUim0YJ1Oh8vllkol5Cj6fL5ut/uOd7zja1/72unpKagc4GuUSqVHjx5xudx6vW61WjFwpNPpGJcUCoV4PD6bzSDd3NzcxBDq7u7u4uKi2WwOh8NisZhIJCwWC1ys5XL5zs4ODMARIQqKisPhoHY6Ha/XS6PRzs/PnU4nJmdgVYC2jKjO+XwuEolOT08xIYfYBLYlsPD9qZ/6qW63+y//5b/8m7/5G6CnmGx1u10kQT148ADhmh6Pp1gs6nQ6mLLe3d15PB7ofLLZrFgsFggEb33rW6lUqsfjQQwEXNg+/vGPl8vlZDIJ1rBAIED+LbotkCxAlCKRSDDFW19f9/v9WEzxeBweUKCrlctllKkKhaLVam1sbKAaIggCQW1Pnjx58OABwkrvlbgKhYIgCKVS+e1vf7ter29ubqIX0+v1oVAI+sVsNjsYDJxOJ5wWE4nE888/f3R0BEQTus9YLAbjXGD+KpUKpTvI2sgN8Hg8GxsbNzc3x8fHIJJtb2/f3d1RqVQwl6B3LJVKIF8hNaJWq4FSTaPRrFbrG97wBgyPQT3AUQX5TygUAraHZJPRaAS3i1KphAkIh8NBhwIO/mq1SiaTsLTD3fXw4UOw12EUYrVa0+m0TCZDqKhKpYINNXYpn8//1Kc+1W63lUrlxcWFSqUKBAIgOCFVZ2Nj4w1veAMc6nHtA4rGpV2tVkElQBQGnOXAoIE/u9lsVqlUpVJJKBQul0sQpWE28dM//dP/8A//AKMmXJAcDiccDrvdbmiUQe3BnAiRFP1+H16BDx48KBQKFArlR3/0R7/xjW8YDIZqtfro0SMk2j59+hQj/2QyCU+zi4uL+Xx+cHCAJEqCINhsNrYiRuy4iVEvi8VipEmrVKp7Q0ZUprB3QlYOiURSqVT1ep3H40F6DtIXDnSj0fjo0SMQF5EjASfnVCoFBADVEC7d4+Njq9WKmHHMR0QiESwXEfEHz/f9/f2LiwsEdxMEgRoEc0zknzwDR589z55nzw/9UBEOymazvV4vTOhgGszhcCBsv76+RmI4pPhokkG5AaMRCfLtdhvBpWA3/NzP/dzrXve6w8NDsLx5PF4qlUKUDmLfjo+P0eejEXA6nfDs/fKXv9xqtdbW1nw+H4vF+vCHP/xXf/VXyWQSDSoksD6fDwpimLLhusYUCrJaKDtBm4VvmEgkug9SJQgCc36YWUD9nU6n4R8B962rqyuwm8bjcTQatVgsmMzxeLxAIECn07/5zW/CE+Dy8vK1r30tnBcgpgIXUy6XA1LBbYBPiDsQw07M/9vtNirq+XwukUhA+6XRaFtbW0wmMxAICASCtbU1EDfARgGUgxgECoUikUiEQiG6cQQyQE7abDaj0SiM6sRi8cHBAbJpoae6JxGihUZ8QbPZBAINcxYw1jAQRTSJWCwOhUImk2k+n29tbSHvCgoriURyfn6OogYRYdVq9dd+7dcIgtje3v7Wt74F++VoNAq9mc/ne/TokUgkOjs702g0QqHw+9//Plgwn/3sZ2ETGwqF7vsFEMDRMIJshvkovClRZgPTReAOtNe/93u/B6eYt73tbX/xF3+Brh5+CFKp9MmTJxsbG1wuF7FbEonk+vra4/GgMg0GgzQaLRwOEwQBb5QHDx7c3t6+8sorzz//PLRqFosFkR2wJun3+9AxwlwLCD3cGF/1qleBUQ4aKJK6T09P9Xq9QCC4uLh48OABJJ0qlUogEKDBL5fLf/zHf0yhUL74xS8Oh0ME01ksFp/PB81Bt9v9wQ9+AHdVMNmhoL26usKCpNPpyJeFrJFKpWYyGaw3RDTk8/n77pXFYh0eHkILihYBLm0UCgVESiA+Wq2WiuoUDh8CgQCBFAwGA4sglUpZrVZ4TJNIJKlUCsjaZDJhagDFeq1We+65587Pz9ls9he+8IXPfe5z4A698MILQqGwXq/DMgAE6lqttr6+DptPxAU5HA6MEv7gD/7g+9//Pl7cq1/9apfL9cUvfhFGb8gKm8/nTqcTbOLBYJDP5zc2NuCm53Q6MZYHP2ptbS0QCLzyyisHBwer1er8/JwgiK2tLRA34YI3GAwGgwFsF3w+HwLH6XQ6EjdgKolEFQhtkEotFAqfPHkiEAigiaZSqX/+538OSQuDwcDc4cGDB0+ePDEajQCG4JyIjvr4+NhisWBsFI1GRSIRik+EhoNiDEYJ5hePHj0Csn1xcYH9DFW1z+fb2NiAWTFIFjCLFYvFqJzBgFYqleCVEAQB1QwCMkCLJggC7Ay1Wq1UKrPZLPZDOp222+2wXwWfAgyCer0OyixcWng8HnKVW63WbDYDMfctb3kLuG04I2q1WjqdBmUOc5DLy0uLxfLGN77x//yf/4NQddj5cDic73znOwqF4n/8j/8BrgdwWUSFgxPIYDDgqA7yCI/HA7OLzWbDJIFMJh8cHIBMAQZ3Mpnc3d0lkUgvvPDCF7/4RbAncIgj0xCnEgQ4ZrP57u4OhBSwkzweDyxR4LAJD+Ryuby1tUWj0S4vL2HChEkHAuvW1tbQoSOdWy6Xd7tdBJjhEER+WjQahf4VFN5wOAz3wJubm8PDQ7PZjCxBqMDe+ta3YiCiVCozmcze3h7ucjabLZfL/X5/Lpd705veBJAb9wpC3Wu12vX1tdfrDQQCd3d3crmcTqcDFG+1WpFIZG9vD8cH1CTgdEDtgexr6FNSqdTl5SXCSeVyOTkSidx7PcBcF8Rh3L29Xo/NZgeDQRxIsPMBSoyPBWcdKpUKH53JZAJTHAqFcnd396d/+qer1cput+OA3N/fh+vJ9fW11WoFIWpraws8qNe85jX4DLlc7sd+7Mfe8Y53PHr0qFqtjsfjZDL53HPPYUtjCgWDFrAq2Ww2jFWTySSgeBBbcdjDdVIkEm1sbMBTOx6Pi8XiRCIBNr3NZgMvFslPXq93b2+Py+UaDAa4n4PVYzabS6XSeDymUql/+Zd/ORgMXnjhhc985jPr6+v48kC4NBqNW1tb4XAYthpwMMcuRdXz8OFDuIph6CAQCM7OzsDPw3SDw+FsbW2JRKJut7u1tYUqoFqtHhwcSKXSx48fEwQBJRFEULDYKpVKGKwAmcJBsLe3h5tZo9E8ffpULpejdsAM+/6ohScQQRBA6eGxAD4l1ECRSAS+wcjoBXO0VCpBME6lUg0Gw/e+971Pf/rTb3jDG4bD4d3dHZPJ3NjYYDAY94lhg8Hgl37pl1772tf+23/7b9/5znc+fPjwd37nd37xF3/xzW9+M2BCbBsw0P7zf/7PUql0f38fCDGsD/b29mA8EQ6HMXKezWZQecOqy+12L5dL2O3A4OONb3zj3t5eo9GIxWJ7e3sYK+KOBG4K6gACTDHEgXrI4XDY7XaQvuEA1mw2weaG/9h9ohp4klQqVSaTmc1mnU4H/R7ujFgsVqlUrFZrs9kEu1IgEEDmIxQK9/f3S6XSdDrtdrt2ux34OvBUzEpeeeUVMEqtVqvFYkEdtLa2JpVKX/va137iE5/45V/+ZdiULBaL//k//yc0Rzj1YCKXSCR4PF4ul2MwGDANms1mSL2YTqcMBgMbAagQagq4PbHZbBgOocA0GAzr6+twJKnValSTyRSPx8vlMv61VCqFNxSLxUIS393dHZIHLi8vodI/PDw8OzsjCGI0GiElFAjicrlEWsQnP/nJ73//++gCPvOZz/z2b/82nU5vNBpwTyEIwuPxwA6/2+1CwvD5z39+b2/v+vqay+X+7d/+LfQLi8UCVmjz+fy1r30tiUTq9/t+v9/r9YZCIZhZQ/9WKpXwIVFE2Gy2dDrNZrNhYMfj8ZAji/g4qVQK973b29u1tbVer7e/v396egqnjLOzM6FQiMhIpCUAx8pms/v7++l0OpFIAIRDZXR3d6dSqf7bf/tvRqMR7HXIeUFVwJAFwc5kMlkmk/3gBz8QiUQAm+F/C3ubtbW1ZDKJjg8OLk6nE1jj8fExCHzILkSQ4u7u7tHREbKvZTLZZDLB1wE1CsaThUIBK6Pf77/mNa+B5VSxWKTRaAgoQ5wflMQ+nw/2QqPR6MGDB2gqlUolLjpEDRsMhlwuh9RYRI0KhcJgMAiTmGazyWazYeO2WCzC4fBHP/pRr9eLbgLXLPKu8T7hzQtSSavV8vl85XIZiLvBYPjHf/xHVJ2BQAAXHcLJEdkjEol8Pt/h4WEmkwmHw8CGYZBVKBQwIUokEi6X63vf+95wOPzZn/3Zk5OT2Wz20ksvfelLX4Lj8Ww2g4QXjBVsV6yi4+Pjzc1NWO/0ej2wPN7ylrcgNgFhrs1mE6xT0HyRsYg4WGhG0Oqq1Wp0r7C8YbPZvV4PITsgK1Cp1FarRaFQisUivspgMIg0ScQGAMu3WCyBQGB/f//k5ATxIxgRvPjii5///OeRu/ynf/qnYrH4gx/8IIfDefr06b2XF2R4aMFKpdLjx49JJBK+cXivaTQaiMX29vYQp6DVakFOT6fTEJfNZjOdTofa6hk4+ux59jx7fuiHDFc+rVbbbrfT6TTCx2HEACgRfSy0twKBYH19HXlrjx8//vCHPwx7SCBtqEdqtVomk0GwMAyRPvKRjyCxEp0IoqFx1IGOlc/nhUJhrVZ7//vf/7nPfS4Wi0FS8Qu/8AuwRXzjG98IXaxerwfpQCwWw7i4Wq26XC6XywWLDWQFIG8FVx9c4XF412q1w8NDxNNDPMLhcJLJJNgyUMQ9evQIgWnAhpVKJQAhtVodi8XwC4bDYcC0yLNoNBput3tzczOdTkPaBJoDeApoyOl0eiqVevr0KYy/gEEWi0WoSzkczsXFhVKphGQD4isYPfV6PQwa9/f3GQxGIBCw2Wz3OMtoNAJ4CeOCeDyOrAO/3w8nBTR3uChQEaDYub29hVEzmLVCoRCkT4VCgXdyn6u+XC5Bh0HgLipBHo+n0+kIgphMJgijQq7V888//8d//Mcf+9jHPvzhD3/9618nkUiRSOTg4ODq6qpcLkejUQgllsvl2tpaJBKZTCZarValUu3u7r7+9a9/1ateJRQKkSoSDAb/4A/+ADginP5B+cGyhI0bOiC73Q6iB5lM9ng8BoNhtVqpVKp7gFmtVnc6HSCaQqHws5/9LJwlAf4JhUIE7tyTjKGivL6+RkwEft9oNJpOp4vFIriqMItbW1sDCw6KUtjWlEolEM+gVIKoBO57tVoNsa+AMHq9nlgsRvoEZJzwuwNyCfB7sViQSCQ4TpJIJPCDUJ4olUoYPnzyk588PT2FRydBEN/+9rdfeeUVFIawC4TMv1qtwsgaEbk8Hm97e9vpdGo0GsA6Ozs7IAROp9NIJILoGb1eDxtqrVYLe22VSkVmMBhIY0f4CMRFaPVh2crlcuFlSKVSgb1RKBRMW9BKWK3Wr3/9636/PxQKAUNisVg7Ozs/8RM/IRKJ4CiDGgnWnnD6E4lEk8kkkUi8//3v/5u/+RuCIH7v937v1a9+NSrkarWKj4Gub3NzU6VS1Wq1VCrF5XLhzQcRtFqths3J+fl5MplEUpRMJoMJeywWy+fzsFHAKwB3s91u12o1u93+yiuvQEIKbS4sEvA3Awhst9uxWKxYLJ6cnODVf/3rX2cymS+++CIAYxC6vvKVr0ynUwAlFosF2L5QKDQYDGAr4fQByQ0lLo1Gg49poVCg0WiPHz+GdSt0vVar1e12I8gSTvTYMB6PBxGZMChnMBj7+/tonpFXBIqNWq0GWwTEHiaTmc1mcYiARLu5ublYLLBw0TaCWQuOPBpgJAzCtbherzudzkgkAuUVCHJAkfCbgixnNptDoRDciePxuEajgTGly+W6T0hBODneQDabBX8/Eok0m02tVvu6170OtJFsNstkMk9PT+83+fb2NpfLBT0E64QgCASXwV+jWCxeXV3hLsQPQrcFZ6A3vOENH/rQhxaLBY1G+/jHPx6Px8H1fuWVVwDJ4XBnsVgQlR4eHm5ubnI4HD6fDw4V1NhGo/HrX/865lBnZ2fg1+dyuY2NDdjK4kBHC4NUrZOTE2SjLJdLDF+YTCYoyARBgDyCaRSPx1utVgCqeTye1WqFgym4W/COhrBNKBTGYrGjo6PlckkQxEc+8hFEZL3yyiunp6cw2dbpdL1eT61Ww6ZrbW0NCA5o9VqtFqFt/X4/m80C3WAwGARBAK3DBQ8IEsJukUgEphUZho5g3UUikXA4jNbd7/ff3t7iTIE7calUIghiPB6j58GLy2QyoVAIQwoMLBG5Go/Ha7Xa/v4+PDK/8Y1vIIcdVjSNRiMajX7961//6Ec/Cqr1hz/84dFoBAKfSqWyWCwsFguOAMlkksfjtdttUFbAjDSbzUAuoQdZLBb7+/vNZhOmQTB6zWaz+EiQXfL5fPju8/n8r3zlK6lUKhaLbW5u4l+BtH96egqpJVpll8uFyZHb7VYqlbC9MBqNSCdYW1s7Pj7u9/upVGq5XOIIwCWD4Q4011hDsLFAnwnaGGiIwPMymQzyHIH2g4CHXw2GUZPJ5Ac/+AFGsBgNAnJPJpNg8V5eXuJOAGyBN4xQokePHonFYpiSoUFVqVTxeNxisQyHQ/hKoIPd39+/vr5GApBGo4EhACa+BEGAOAv3DVgiWCwWhEVgUD2dTtPpNPQ1cB4C0IPxMMD1ZrOZTCaLxSK2NPw7IJKy2+3r6+vz+Xw4HOKUhGP4vU8twh9Qx8lkMqVSGYvF5HK5TqeDF9loNIJhJ6i0g8EgGAyur68Ph0Nwujwezzvf+U4cgn//93///e9/XyaTPXjwAA7pcOgFcEgQRCqVwjWDag5cW3CrUAbCdQWXLolEwvCbTqdD64x4EIIg5vM5dEbZbBYZvXw+H0PiRqOh1WoPDg5wCNbrdVR88EAQi8W1Wg2RfQjo43A4QKw2NjZg/g6SRLvd3t3d5XK5k8lEoVCg6gTRE+ESBEGoVKpcLhePx1EXQ5G0tbUllUo7nc7a2hqbzYY1FMRWgNtMJhPg8O3tbSxjmIaRQaZ2u93JZFKv19vtdjA7Hj58aLPZQLY7Pj5OJBLgX8BeGOfQ+fk5ZNcbGxuLxYLD4Zyenvp8PgaDYbFY9Hr9/v7+b/zGb7BYLIFA8Gd/9meYpz569IjJZH7sYx87Ojri8/k8Hu/Nb34zwF4gi7lc7vb29rOf/WytVjs9Pf3CF74gl8sDgQDGCtiQOAXlcjnaELgSwBYc5x0MYKB9gLIIhsAymexXf/VXSSTSxcXFt771LXzs0WiEPDEymYyCrV6vo8BWKBRwZICp71e/+tXBYIDkMRhk/eEf/iF08VKpFAsIA04gjjgpptMpNH6Y8iwWC1Rwdrudx+OB1edwOJChm0wm/+7v/g7O6aA/53K5crm8vb3darVQ02L8iamb3W7P5/NyuRwJLJubm/gVwFbkcrlPnz7FJgRHHpkJXq/39vaWwWAACIdrw9nZmUgk2tzcHA6HIPUgcAAe5SQSCXsYgXvD4RBB0IvF4lvf+haPx4MEAY2P2WyGZAnXUqFQaDQaDocDPBc0iZCQ6HQ6FN5YYGq1+oUXXkDj2el0/ut//a/I8cWojk6n42TJ5XIcDmdvb+/JkycwQ0fXw+VybTabSCQC7sjlcuF4EovFMCngcDif+cxnPvShD0Uikb//+7//9Kc/fXt7C1eH6XQaDocRIrOzs2O1WlHORKNRXAYCgeCrX/1qLpfT6/X/9E//BNi7UCikUik49UN4AonD8v8+BoMBnEtYdhMEcXV1heDRyWTSaDSKxaLJZBoMBqgrW63W6ekpMEtI3Y1GYywWg82SUqlEGDMM3LHhMUN43/veB5tIdIg4T8FbN5vNuVwuk8lApabT6fx+/9nZGRpMlDw2mw0U/kwm0263gVJTqVRM6G9ubiKRCOjOMpnsGTj67Hn2PHt+6IcMdyNwcqhUaiKRgHcmckAxwnS73UjlhP3neDy+uLhwu906ne7BgweI8EQ5p9frwRbDIAr2uVqtFkK4//Sf/lO1Wq3X67/7u7+L+9bj8Wg0mp2dHalUimQgk8lkNBpHo1EsFkPyOwiONpsNCRpIcke4DuxRuVxuLBar1+vhcBjGtlC4YTZ+dnbW6/UgoCAI4uLiYmtrKxQKdbvd29vbX/mVX4F3KyLpEbZ8fn4OvSYUAXK5PBQKFYvF29tbmUzW7Xb39/dtNhu0WPAZwpwfMXFQyuPgF4vFjx49WiwWR0dHw+FQJpPBYwZeuPP5/OrqCtlxsViMIAgSiQSuztOnTyeTSSwWS6fTLpfLZDJBmw8FFJy4ALJg4IcZJ6poyCsQ/hwIBB48eNDtdovF4ubmJgRsmHBD4oyMNVgi0Ol0m80GFQ8qHfR9GxsbMFVOpVIajQYjOpVKFYvFGo0GftDd3V0sFut0OpjyVioVFMzIDUDHd3d3V61WnU4nOMfL5RKASL1eh7cQjPw2NjZ+/dd/3Ww2VyoV3Hsw+EKhTiaT0cMig1Ymk81mMwaDgWsTE0SxWAyJF8S1y+USc9NXXnnF4/EkEgnIsmExiaSF7e1teCNBvVIul4+Pj4PBIIpcCAhjsdjp6alQKAS6BxLjwcEBuMtQpsN2WyAQKBQKl8sFyywAguCeQXgSj8cR2oJKBFNYWApmMpmdnR2BQEAQBFy5IN6HSzDGCBj6ItsBfCsgoC+88AIShcPh8OXlJaxS7XY74HOIp2Fwh3C1yWRiMpn29/eVSmUymaxUKhAZeb1e6EjxQiChWi6XiLDodDpklUoFjVA4HIZ+DIOMarU6nU7tdnssFoP6BYhUv9+HaB2rv1AoPHnypFQqwfd1sVisr6+7XC74o4VCIblc/tGPfhTuZpeXly+99NJf/uVfkkiktbW1f/Nv/s3rX//6L37xizAyi8Vi1WoVsMX+/j5i8hAngbIN2fFwE1AqlblcrtlsQnxFEASCrVQqFVRP2Wz2Po680WhwOBzwWyAbM5vNHo9nb29PIpF86EMfevHFF4VC4b3MFCcRjMsR/MNgMNhs9j/90z+xWKx3vetdaFOheYeOC0mL0Jhj4rC+vg5TXAqFAktxaKvvPSN9Pp9QKCSTyZBLYnoPteJoNPra177mdrthw5XNZhFWvra2BqbTdDoF5ESn029vb0OhEKSicBUAysVisVBVwuLB7XYDp8QxB29eUOAQpgM4ACMtGPaClrq9vV0ul/f29iAwBWbEYrHA66lWqzgpZDKZ2+2GSbXb7UZYMWCRxWIB/TuczUD2sdls29vbSMOEaWC/34fUCpGgLpcLHu6FQgFO6Mj6A9IEquhkMgGchI2KFoxCocBWjiAIABNYRZ/97GdhSoCW+T3vec/P/MzP2Gy27373u9/5zndge3FwcBAOhzHa2NzcBNC+vb2NaQvAL41GEwgEvvWtb2m12vPz88vLy3a73Wg0ELmE45tMJkMwBvIRfEMAnGs0GtAaa7UagA/MH6vV6mg0ms/nsFYAJw2byOfzYWMPh8PJZKLX68FGw4JRq9WZTAbeXBaLBYdRJBIRiUTgHGP9QPmtVqshzp5Op+iv4asaCASQ/gVnCUjjkPgH/Pvw8FCj0Wg0GlCoyZjFolKAxAPgh8PhAEEdkKxEIimXyzDyiUQiYPiXy2UY8CuVSrBrTCYThUKBVF+v14N7OxgM1tfXHQ4HJNjf+9735HI5bHJpNJrP54PsGpY5UGp++MMflkql8Xj8N3/zN0FJgnuFUqlsNBpozmFHCKx7uVxiaiASiSBqBPYLibFCoQAsDB4a0oze9KY3vf71rwegSCaTP//5z6fTaRCQHQ4Hvgw+nw+Ebz6ff+Mb35DL5RaLRa1WwxoHybiDwUAgENze3r700kvdbheOLBB9Arg5OjqqVqvr6+vL5RL1AphaarU6mUyur6+fn5/DoQMzLMAKGo3mpZdeUqvViBpEEg9ELo1GA2xrDMmwCcHRgpVDJBKh0Wj3col0Om02m4fDIXiTgLguLi4ajcb6+joSg3CmADq1Wq0XFxder3c2m7lcrpOTE6PRWKvVYKWHv7PRaIAk5na74XsO8usHP/hBk8kEMztMc7a3t8Gvh305QRDT6fT4+BiQdiQSwcmCfE/ceGjO3/jGN0ajUbPZzOfzMQqRyWTxeBzMbvDlkZ6FaHjM9XO53N3dHfY8VLyvec1r4vE4ahM4KkqlUpPJJJfL7Xa7VCrd3t6+uLj48pe/DJ0urGHu/UFubm6i0ahYLM5ms16vd21tTS6X6/X61Wp1e3urUChwHGxsbAD4YzKZjUYDgAuEvPAuAK3h7Owsm806nU6otJAc4vf7weKTy+VutxtQbq1WSyQScJb2er35fJ7D4YDldXZ2hsWzWq2QFwXnSnBAQSSlUqmYtlKpVMQvSCSSjY2NSqViMpnUarVcLhcKhQBKoeeyWCxCoXBvbw+goclkUqlU8CiGqAp5pjDiJ6OqyeVyEokkkUhACLxYLJLJpMFgALCs1WrD4bDL5ZLJZBqNBtgPVGTr6+soEdfW1vh8fiKRCAaDtVqt0WgQBJFIJOCN/uM//uM/93M/hxwTHHJvfetblUol7FL1er1WqxUKhVhGOGsRXVmv1ykUisvlarVaCoViOp1i6DidTj0eD9i1EokEBAQul4sYFxaLBTsfaMOQOQILIyqV6nK5nn/++Vwut7Oz88ILL/zoj/4ok8lkMpmf/OQnUThgjigUCkF1LZVKKE2Xy+WrXvUqpNgCH83n84Az9/f3z8/PEc+FaD8Aiv1+H1ZLg8FgZ2cHJ1csFovH4/F4HCapDAYDZBDg3mCvg6gSiURgVGuxWOBPyWKxsFjh8UmlUhFGB2oDPC9msxm885lMpsfjUalU+XweVFqwvwUCAST5oHhMJpO7u7vlcnl6emo2mwmCUKlUkB3AXzMej1MolOVyidwGQOYwTyoUCpDnAKXO5/OVSgWBCZPJxO12YyYKNXowGJzNZvihjUYDMXd7e3s+n49KpV5eXup0Otx+cJDd2tpKJBLwN+NyuQDy+Xy+w+FAeXhwcIAQkFwuB0HQbDazWCxPnz6F2C8ej8OFoNPpmM3mwWCA8ND5fH57e1uv13/6p3/6Xe96F2rnd77zneVyGd0NBE0IXoZvy3g8JpPJkUjk3e9+t1QqxcUGwLXVat3e3t4XIwKBAFkNMDTncDh6vV6lUsG+HAl7kLHBzgO91Wq1glU6zlMqlarT6Q4ODqhU6vn5Ofi7GE3s7++nUin4odhsNrjeYySEqD2xWIzvrtPpwJ1/uVxeXV3BPYhEIp2dnSEvFsl7wWDQYrHAsZkgCKfTOZ/PEQ8EBkahUOh0OoiJxI4jczic1WplMBi4XC6G9kiQhimQw+HAdp1MJmAEBwKBdDqNJASQ3iGT/eY3vwlWP5vN5vP5ZDL59vaWy+WazWYA788//zzGhx/5yEfe+c53YrAsk8nQnQIRAM1BoVCAoN3r9XZ3dzHchdsNCjkymXzPcep0OpeXlxiLyGQyTF5BYEMioVAotFgsZ2dnqAsSiQSNRjs6OjKZTOFw+Ed+5Ed2dnYQEEmhUJ48efJf/st/ASShVqu1Wm00GiWTyfCbGA6Hq9Uql8ul0+lCoZBIJLxeL5/Pf//7308ikVDeQ8N6fHyMTa7VakEYgWIN9h+r1YpOp8O1BGMIeIgUCgUwx0UiUa1W++Y3v4k2AZg2fIwxF4B3CxhoyEBiMBij0QjxFDs7OxjCwc4rk8lIJBKtVgsMCzwOtLjIK2Iymaib4A6P+wO9G5VKhdyz1+vd3NxAq4INXK/XLy8vYfaFGmptbQ23OozOZ7NZKBSC/102m4UoC55Jq9UqEomAih4MBmE++OpXv7pWq5XLZS6Xi8T2u7u7g4OD8/PzdDr99OlTGBHDWn06nWJCAccKDoezsbFBp9MRne3xeDweD/wQG42G3W43mUygpWClUalU2ERvbm6mUilIbEgk0m//9m8TBGG32xkMRqvVguPZwcHBzc0NDITgVIIUi+985zudTgeKAfQFmUwGDEAQt0ASMxgMmLzI5XJcDOCPoWny+Xy1Wg2ZGARBRCIRsViMdrtcLvt8vkwm43K5EBugUCgw77+Xt0KriuQUyJqhz5xOp7/1W7+VSqWKxSKZTEanDJJIJpPZ39/HRBICSKVSCRAK1AqIFUAUQoYuzFkwAIaK4tlU5dnz7Hn2/NAPebFYINOwWq3SaDSo1Hd2dpRKJUEQ4Cy1Wi2VSoUBCkEQXq8XDq5oqhFlgviSxWKB/Pe9vT1gKr1eD14Ao9HoXe9611/+5V9qtVqADoPBAD60UEBCBC2RSFgsltPpnM1mP/mTP4nKot/vz+dz2MDWajWDwQBy4X2E130+NjIsCIIAzxXGn+PxGAI20GT9fj8G3fcy0Pe///2/+qu/qtFoLi4ufD7f1dWVQCC4ubmh0WhowdDpvPjii/AccTgc8/nc5XIBV4crqt/vd7lc9XodH6ler/t8vkgkAiIm+JGg5YAuBSAN8Y6gpWGihHS16XQ6GAz+4R/+ATxfuCJD7JxMJnEta7Va3K4o3AaDQSqVikQikIGhBkGsFJw4oIhFWk8ikbDb7WQyORQKCYXC3d1dVDQXFxfI7obCOhaLXV1ddTodsIe5XG6324URoVKphKfJd7/7XXDhYW0N5A8NM8jLSKJAhoDT6UTckVwuN5vN4JJDnwZyh1wuX61WAFy3trZOTk7QrOl0OpfLpVKpYEJxdXVFEATGN0ajsdls+v3+tbU1ZACh8FxbW8Nf63a7ETdVLpdBqOv3+5ubm2w2myAIjBX+6I/+CNhhq9WqVCqj0QgRJ61Wq1AoQK8slUqBzZHJZDRBqFOGw6Fer4cYEvNB5Dlh2TQaDVi00el01AgIZj84OABY4/V6LRbL7e3teDzGaKbb7cJXTSqV3mdKMJnMUqmUSqVAC0KVHQwG5/O5Vqu9uLgAh+34+BhkEOyX9fV1aCD29vbwrjDfQADY3d0dhmLwzWSz2QD73W736ekpDFDhpxGLxVwuF4vFAv2PjPRt9I0EQYBge3l5CeD64uICrid0Oh1ifiA99/lDyDojCALzTtAQgb1tb29rNBqYJmMeRqPR2u02IkKRpomcV5FIBMh9b2+PxWL9xm/8RqlUEggE+CbW19fpdHqtVru7u4MNR6lUSiQSsAvv9/s6nc7pdPr9/mg0GgqFNBpNsVhkMpng7aGftNvtGo3G6XQCTPZ6vWw2G8lmiFDtdDof+chHQMi9vb39d//u321vb4fDYaSBvOtd74I5O/pGGo0GQjpsB8GI29/f/+IXv4jRklwuR9VKIpHAlYRpKyj59+E90EFcXV2huU0mkzhtP/GJT/zoj/4ovjwQ2xDwCTGLy+WaTCbIHAmHwzc3N0dHR4VCodvtbmxsQHiKiNlMJtPr9ZxOJ96G1+tFPCISmOCuDO4QaPsAlV0uF9I90ELDKYNEIvV6PQSXgByJNhtd9HK5BGUTrhZra2vRaBQCYofDgWYWgs5741ir1XrvQQmlLGa3TCaz1WqB4v32t78d8lm5XA5pT6/Xg/xHKBRC2I2tjnTBq6urRCIBMwHwU8GLQzoPulQw0PV6PUykcW4C2/r4xz9+enr68Y9//O/+7u8wGWQymTDaefr0aaPRwD6HNkSpVNrt9vPz86OjI9jQo5dBv4b5KO5FuMnKZDL0d3DzB9aA+ReIbQjlws7Cp4Iw6l7Jjs4UZ6jf77fZbMvlElk/yC2FuAEuqjqd7s1vfjMk7xhK5nK50Wi0Wq2QybK7uzufz6lUqkKhoFKpfD4ffFZ4U02nU4gSsEPB+p/NZmjYJRLJ/5uBcB+GAKWtTqcTCARyuRwWIwDwMIJ9+PAhmIu1Wg3mw81m02g04rdVKpUY+GGKBvSLyWSCmAgPVSQkwAG03+/jl2EwGMjdguKAw+E0m02Px4NWFuHVVCoV4guz2axWq8PhMJQgBEGAlYCM6HQ6DXkb+IjYw5BRY31YrVYEFwFeVqvVm5ub2A+f+MQnQABRKBQf/OAH//f//t/z+TwSiWB6ZzabUQh0Op2NjQ1YKmCQARk+zIGoVGqxWNze3obpFiKv6XQ6LFgikQiMsMhkMpfL3dzcdLvdGPihtUZig8PhyOfz5XL54uICS4dEIqGZr1arkISjf7bZbAh8mE6nCCWAKgyeMbgxXn75ZeSAtVqtbDZ7cXGBQw2cUYy0KBSKzWYD3gEhAuKyRCIRjFTpdHooFILnM/xKNBqNWCzWarWVSqXf76NPBhyIWM/5fF6r1QBGcLlcEolUq9UQQ3t6egrqNOqv8/NzsHWSySRkEUKhEAOOVqt1cXGxXC7h90On02F9mkgkKBQKsINkMjmfz/V6PXA08OJhFotlALok5i9Op3O1WiUSCWwM4BFICNvb24Ot1O3trc1mw93m8/keP34MNy2YNkBINZ1OV6vV7u4uqjlYxt3d3VUqlc3NTRKJBC4s7ELhuyEWi6+ursAnBqALxj1OQKSUQyAKhdva2hqi4drttl6vx8ExGAz29vbodDqUB2gIMLJAkCvkiBqNBn0AfsRqtcLrRS4kQhUMBkM8HgfRFocI3GpYLFYgEAD4iLMMKXwIPOXxeGRoLgCBDIdDu90ONj68VYB1N5vNRCIhFAqRIokCYTqdxmKxZDKJshDZeZjDF4vFjY0NLLVerwe+Sq1WYzAYV1dXWLJqtVooFMJqEI5Sm5ubMIzb3t5ms9mf+tSnULBAZQBzd5gA3ycwwwqcxWJVq9VMJmMymfh8/uPHj5fL5X1GptlsRqUDCRNGj/DdBYsZFRZAShqN9uEPf/iFF17A5KlarX7pS1/62te+RqfTYTZrMBhAdQN7F74+4XD4xRdfhKYTqSsbGxvxePzo6IhMJqNFgo8bMt8Qboh+6urqKhwO397eNptN5ADDo219fR3ea9/5znfg0JfL5ex2Oyx84Fq2s7MD9Qccuh8/fgwjH3R8yB9st9s//uM/7vV6e70eLJ0dDsfjx4+VSiXIEdlsFghZu90Oh8O9Xk8qlbpcLljRgBg+Ho+73a5EIrlfhVQqtVqt9no9v98Pc12/34940Gq1ek+URkHqcDhgIk2hUBAKTaFQNjc3W60WKh0kPxqNxuPjYwhzsAjBrIfrL/hgt7e3Op0OB5nX6x0MBjiGHj9+jGoIty6i7XK53NXVVaFQ8Hq9yFR/6aWXcNsjSRsObH6/H1HPrVbrF3/xF6HE+8xnPvOe97wHsazI/QT8CaNGkPFSqZTf76dQKIFAwOFwwOwa8TqJRAL9HZRZCOgB//3555+HlxoOSr1en8lknE7n3d0dcP3ZbAbfwEwmA80UFFhIMo/H4zwe7+bmBlR6OEt2Op2zszOFQvGDH/ygXq9DlQOlD5PJBPUR8AImmBqNBmJuMPrS6fTzzz+Pflan04G8B/8t8D4w5eh0Oru7u7AXeQaOPnuePc+eH/ohy+Vy5NzgXIdZLtwr+Hw+YmkoFMrW1haIOo1GIxKJeL3e/f19CoXy+7//+8FgUKPRIE232+0aDAbk3YMSgzsnGAwCZ5LJZKD6LRaL2WyWyWQw1kbEEYwq0ul0IBAAb+Lw8NBut4NaDkAOxAoGgzEej8HgzufzSMYej8eRSOTu7o7D4RwdHYVCobW1NXhqbG5u+nw+vV4/GAyOjo7Q9q9Wq+vra2S7g2CHC+2FF16AzBGJynDWs9vtoVBosVigclutVmgfZDIZhUKBQSNocmw2u1wuw7qOxWIxGAzgR+hrAN9kMhmxWIxG1+l06nQ6t9uNnCStVuvz+Uql0q/92q8BifjsZz8LZ+Butwu2tUqluri4QCotnU4HaHdzc5PL5YRCoVwup1KpoJbDPKrdbo/HY7ztVCoVDodBHCoWi7u7u5FIBAQtgiBgCIbWGgwIrVbbarUQ87O/v69SqS4vLwmCoFAo0WgUFn50Ot3r9b7rXe9C+gnmi2DBpdPpdrvdbDbhvSYQCPL5fCwWAywHHBcs8ul0CrQSBDPEsuBbcDgc0JihBE4kEuhAwW4oFApInGGz2Xt7e0wm0+l04pKHLv4XfuEX8C0AgUKm7MbGxt7eHgQHgC3hxvq2t73tta99rUQiWa1WH//4xwEPAZ70er3ZbLZWq+3u7n72s59lMBi5XO7Jkyc6nQ6d/8bGhkajgSEIiUTqdrsulwu+fnAPxDpnsVgAU5PJJLA8oMjAIMG6dDqdIpGo3W5jCEAQhMfjSaVS29vbx8fHSBoHcxzQEhCfRqMBX9LXve51GEogogRulQKBoFQqQTteLpftdvvu7i6wp1gs1u12UbJhMHx3d6dQKOBgCMV8pVIJBoPYOGREOWHajzB7GEbAXbZarc5mM7vdDjRutVrhawbYeXBwUC6XYWQYjUZhmtDv95GKXqlUkHh4eXmJMAsUwHB5BOwEZbHdbq/X681mczQaveUtb5FKpV6vV6/Xo8uKx+N3d3fxeBzC4UqlotFoOBwONMLdbhdlJ0pxtVqtUCj8fj+6A6jam80m8Bfw8HZ3d8fjMU5Gt9u9t7dns9l0Oh10h5PJpFqtfvWrX/2Zn/kZLpf7yiuvjEajT3ziE8FgEJky9/mDSKtKpVIbGxsEQQCSxKYCGw/Qw2g0wssB7xPTkM3NTcS1MxgMUPHQbyNjHQMOWKKrVCqFQvH2t79dqVRGo9Hb29tIJALXDCAUcCcB5XRrawvwLZwcwbZMp9PT6fRnfuZnoLnodDoQTaD8Pjs7s9lsRqMRPDez2YxveblcSiQSxIWJRKLhcHh5eYnG0GQygS+v1WqdTuf5+TlYc8ChwEkzGo0KhQIrL5lMgoCL3UIQBGJGkUcTDAbhdw0XT2iFYUpsNpsfPXoELCkWi4ExjBgkHo+HOPHT01MQqBHsDnINUrVKpRIipiFNQvrk7u5uqVTCS8Zul8vl4LmC9L25uelyuV796lePRqNOp/Onf/qn0+mUIAj4fWOnxGKx+1DOQCAQCoUAKwAbBjMCaZvJZBIAP84RJLE7HA4MMZF0DVtmkUhkNpuRKwaRK5Lo0LFincA/AaAV3KEwi1Sr1e9+97u//vWvI5FLpVL5/X6VSkWn0zEBgPVvNBqt1+vw62QymaPRKJlMdrtdfGCpVArqKrwjnnvuOalUCgAIjscqlcpms4GVQzYYDMCrXS4XyOYgzF1eXpLJZL1ej2uTRqNBNZTJZHQ6XSAQIAgiGAxubW2BA87hcHq93ng8DgQCmFSB7IhTH1AN5MkwNej1esfHxzANh00mZDIUCsXn8yUSiYuLi52dnWq1CgNxKIWA604mE8Qawb51a2sLFDWg7hj3gPCKISW8D2KxWCqVMhgM0MVAc1Eul6+urnw+38nJCYQkGC1XKpWDg4OPfOQjQDQwjgmFQsC3YU6ZzWbD4bDH4/H5fJBaIeggGo0iXwI0R5gd9Ho9KpUK/CKRSECrEgwGgaj3+/1arQZeFp/Ph/MrkplSqRQUCgqFAlk+s9kMgjqBQNDpdBBZBBPwo6Mj+J6cnJyAxo5xOJ1OX19fhxUoRu8SiWR7exsrEvg/nL5yuRxkFzCbMZvN8/m8UChgliEWi1EXYFACoybQed/0pjeZzWaomXq9XiQSQcIWg8HY2dlBTBnMGeAmj5oFArbVagUGFKzkNBoNbMowayORSJ/5zGf0ej14SgRB3POVt7a2Hj58iMAnOCTrdLqnT5/CU0ur1ZpMJjqdfq9vKhaL19fXGDmFQqFgMBgIBBBJCVEfTLpgH/uhD30om802Go1PfepTiJiAdgkxThQKxePxwChcqVQiuwuXH/x+rFYrm80GVAyLKWwHsVicz+fhq46zA8YZsFza3d3FPYf5KwidoAWCJbC3t6fX6zc3N4GmLxaLV1555cUXX4TIDf7Sb33rW5lMJpjH8Xgc8DmEphqNRiqVAo/DebparTBXQuQATHBRgMDdB2oPDoeDArzVaq1WK/JsNut2u5ubmzDmx8K6uLiAGqfb7RIEwePxQqFQo9FYLpfI0YDPPXisarUaahmJRAJrLzgj1+v14XB4fX2Nq3i5XKKQ02g0aFsgciGRSPC5gsE0plxve9vbkNVMp9Oz2axarUb+cDKZPDw8FIlEyEYFxpnJZGCRgMzHVCqFccNqtSoWi3w+H2Pj/f19r9fb7XaRLgEz9Gq1urm5KRaLHzx4EAgEZDIZDIG73S40l3/4h3/4hS98gcvl4gQEiwEmcXAqLJfLuPz/8A//EC0JmCZOpxPEfBqNdnZ2hj3c6XRubm6sViuGPk6nEzi20+lEsVYul6vVKkx02Gz2xsbGV77yFega3vWud0kkEg6HIxaLx+MxQo+lUimIIRgwYxCAKA2A0AgDhoYKWQc0Gg2+FUDjvF6vVqudz+cOh8NoNMLFHgM5LpcrFAqr1SqEfzB36fV6iURCrVZXKpVCofBXf/VXyI6o1WonJyegYBMEwWKxEFkKKyoA1Rj70el0hUKBC1Or1cIPHasTR084HHY4HKixX375ZWjwYrEYbguCINDIgHoAqaHb7Ybc8fr6GoNMAOGgb7Rarde97nX5fD4UCs3nc0wDNRoNiCFms7nRaKRSqfX1ddB/S6XSgwcPLBbLpz/9aRT5v/u7v5vNZqvVaiAQYLFYUJHEYjFEmWSz2Xa7jVcEeRTc6tDiIUq9XC4Hg0GCIHq9nlAoRIgBNqHX661WqxsbG5hRKhSKR48eTadTp9MJw/RisYgTQSKRwH+EzWYXi8Uf/OAH733ve0EVB8dapVK99rWvRfTnYrHwer3IAMV6Bs/l+vq6Xq/DnhIzAb1ejzg7CKnq9fqDBw9wf2Dchjx2REaazWYej/cMHH32PHuePT/0QyYIQiAQpNNpJPeazWaHw4H6vFQq1et1qVQaDAbBz0dlYbFYRCIRHAHAw0cBj/LVZDIhdB7iUbDrESaMiazf73e73Uwms9lsQllYr9dzudxisUin0xAFg646m83q9TqdTs/n86icQeAnCAKibwjkjUYjIoLRHu/t7SkUivu0pFarlUwmYRoYjUZhg7harcrlcjqd3t7exnD36urK6XRC3sLhcCwWC2aQy+Wy3++Hw2EQlsxmM8pU3HW4doD/CYVCn8/X7/ePjo6KxSJcIcFxkkqlfr8f0cqAKur1+mq1Ojk5yWazaKGdTidmnEi0ub6+plAoiDt5wxvegOb53/7bfwsCGGKuWCxWLpcDFyYYDCKV3mg0Ap9eX19vtVr/+I//2Gg0AP1A7+ByuZrN5snJCdCW09PTTCbT6XRgwoI+CIHPZ2dnJycnBwcHSM+G2zCVSu31erDhQAOFSSSIdvF4PBAIwCwWZl/4KlGig5QBiw2MRcFzBfOSTCYHg0GE9cDlFGGoy+USQ3QosgAoKJVKVMQoQ7Ba4P/qcrmgyYaGe319nclkxuNxBJRYLBaQoKDjgieb1+vFBHQ+nyMwAVqn1Wr1m7/5myDjfOxjH4OVlt/v1+v1dDr9t37rt0KhUKvVevTokc/nazabAIOfe+45lMPQepTLZfxVk8nk6urKbrff3NzgtkfNxWAwoCM9Ozuj0WiJRCIWi73tbW+r1+vlctnv96PH3NraAq/qk5/85L/4F//ir/7qrz73uc+BHoqcDZPJ9B//43/8iZ/4iZ2dHSwMROHAmgyqaEjSwevV6XTwuyiVSoht7/f78NOBZAYrczqd5nI5xE1DJZTP56kIjrfZbOPxGODw5eWl1WoFDDEej2GRiu8VxD6CIEAXgdtqtVp98OABAv5Qel1cXNhsNjqdDoYMRLsoqmHBhqBpGB8kk0m4jNBotD//8z8nCKLf729tbSFuGoAIiAywNru4uIjH44A2lUolvgBUU7FYzGq1BgIBQFZo6jAPCgaD0Wh0c3Oz2WwSBIGSEkx2MJpAJ0Ec1Hw+9/l8oHsAM0MJjcwngiDwadfX1zudDjIZR6MRj8eDhh3IuUAgCAQCWq02nU4/evQI3EEwgobDoUajwXAehC5kCEE6mUwmsfGQ8VGtVrlc7u/+7u/+9V//daFQeMc73vFLv/RLjx8/hh8vAFf01UAiQBCAOhnC3I2NDSQkQuzbaDRg+Qut997e3tXVFdjrRqMRnheNRsNsNm9vb6N7UqlUUH+CbkClUuHG0Ov14L3gcDg2NjYAMQB1a7fbxWJxb28PwlkGg3F7e4v+PBQKWa1WeNNqNJonT56AN4lMKXjnwvn57u6OIAiJRGI0Gmk0mtlsfvr0qc1mSyQSer0ebuPo/EFn4nK5PB7v5OQEHRnI9fAEhYa4XC5fXl4CO8RACgIwn8/ncrmA0Hm93nvPB7C/Dg8Pb25ubm5u/umf/gnENmCN8/n88PAQphhCoZDP5xcKBalU2mg00AoZjcbBYIAQv1gsBoOYQCDgdruhQqhWq/f0KjqdjksU8DYuKq/Xe3R09O53v5sgiEKh8OKLL97r5ZPJpNVqDQaDDodjOBz+8i//8mQyAXSq1+txKgmFQoyE+v0+m80GkxiSfHjowmUWGV0ajQZXCAiZEHlDuf/888+fnJwsl8t7hhgZKBTUxPAOgLU3LhCXywUWLTL4CoXC/TgKJrEKhQIMX0j0xuMxYnuwfEUiEQz+QY+fz+dgBG9sbEBLAs8LsJKxoPl8/pvf/Ob5fA4ssN/vQ9rM4XBg5LO9vU0mk8H1FAqFiUQCRg+QyYMZDQMFNpsNwgy8Ob1eLwaHmUwGtHQGg4FgepQhELbI5XLArnC4YLFYSH46Pz8HfL2/vz8ajVqtVj6fx4dkMpn7+/voYME4hkk3jiekxvJ4PAxQDQYD8GAMlYH2YVAVDAaLxeLjx4+lUinS4XK5HKz3R6PRr//6r9/c3DAYjD/5kz/5xje+AZogbD7EYvFwONzc3ITPvdFotNvtsLrp9XpHR0f/6l/9q7W1NY1Gg0A2cLowjjk7O8PnjEQiw+GQIIjZbLa5uQlvBNipkUgkuVyOQxngmcFgKBaLOHCh3cDAslAotNttnH1wyT87O5NKpaCZ3dzcBINBg8EAj/Lz83MM7I1GI6KnlsslboWtra1CofD9738fApxHjx4h3Z4gCNDh4XsyGAwA0PJ4PCCdcFSfz+cWiwVoRS6XA4sMNV0ymRyNRoPBAIGk6XS6VqsBSoTtC0x0gPKORiONRqPX6z/96U+jIn7ppZeMRiNBEFdXV/F4HHEBX/ziF0UiEeQOmUwGbqnT6RR4BA4CuPzDsxMyFljkm0wmAJbINsadDSlWp9NBhvGLL774//w//88nP/nJ+5CQYrEITHdtbY1CoXzwgx8Eb5VCoQiFQhSqcrn8yZMnPp8PanWfzycQCEgkEi4tiURSKBSCwaDb7TaZTFKpdDgcrq2tsVisra2tTqdjsVjQK7hcruPjY5vNNhwOwRydz+dkULA9Hs9qtUqlUu12e39/f21tDZJqLGuwyPV6vdlsRlry8fExUh5yudy73vWut7/97VwuN5/PQ+BwTwk9OTmBTgSi8sViweVyEZk1nU6BEYITibHr8fExXOpBCoZICRRyuCLD/PbBgwewNoJyDB6Fcrk8Eom02+3nnnvO6XQSBIFrDRp2NpsNR+zxeAwsWiAQALINBALQ+MMbhkwmo0LmcrnhcBjeFrBXAsD55MkTJBvgVWJCkUwmzWbzu9/97m63C2iWw+G43W7U89lsNhaLra+vo3TCSbq2thaPx8/Pz+EG0O124cuA8xFcaQaDgRRCCK6+/e1vf/rTnzaZTLFY7A/+4A9Qj/B4PL/fr1AoLi4uzs/P0U3gO3r/+99fLBZVKhXGn4iAgybQYrHc3d0VCgUgXth+tVqNSqWi2HG73Twe7/r6+uDgALZghUIBbuDgOOM//Ju/+ZvJZJJMJhGCA4RyuVxiDAkKI8Kie70ebLhns1kqlbq4uODxeK1WSywW+3w+2FWA0OF0OvFW/X4/k8lEoso9eRdOxfdHv0qlgik5KPaQO+DdAkEHRvua17wGzGbi/w7LIa5BO4ySrVwug8ACCSJ4yc1mE/4UX/nKV9AC/NEf/VG3293e3haLxe9+97slEgnix3ElQLMP/YTb7cZhrVKpcMNDyYWADuCpd3d3i8VCIpEcHR3VajV4RyPJFUb/Go0Gdn6r1Wp/fx9u/qBN/ciP/Miv/MqvvP3tby8UCnBpsNlsuBsg/rBarWgzTSYTNJbr6+vYUAiIg11jpVLJZDKlUgmQdqFQcLvdPp9vPp9j3Aa3TbDdLRZLuVx+Bo4+e549z54f+iFDGpxMJjFVBbNrPp/D5+ri4gKSGKFQiDk5h8PxeDxSqRTzfAzkEbEDhQ+m08lkMpFIWK3WUqkUi8VARUXOFWqZTqcDSSXut3g8jgg1yCuQ0DUYDGDeiTFwPp/P5/N3d3dkMtlqtcIMTiAQhEKhTCYDT5pWqzWZTGCdhr6x1+shiRrhIGq1ut1uZ7NZWG8yGAwYZMLlKZvNnp+fw8gfqhmQxDCmwt+2ubmJ1BK0x6gOEK6J4rbdbr/nPe/BjVEoFHZ2dlgs1v7+PkZii8UC8VQAdyBOrdfrMH1pt9uFQgFigUQi4XA4CoUCCBTgCNLp9F/8xV9UKpUGg+GDH/zge9/73sFgAJgWcXnPPfdcIpFAcAzAhZ/8yZ8kk8nRaJTNZiNk7Pnnn0c4aCaTYTAYLpfr4uICxDyz2QwGEX6WWCyGUq7RaJDJZAaDIZPJ2Gw2Gorf//3ft1gsSqUSppNsNhu4OGBs2JcVi0WCIKADAk2gXC4PBgOJRGK320G9QcIQ3HcFAsHR0RFsTSHPRQPFYDCsVmun08E31W63Qbdpt9uwF4JFtl6v53K5YFuiAIG7MloPNpvtdDqdTidcCOGXBdNmhLCjFM1kMogEW19fR4eCQJ/Dw8PhcNjr9b773e8+efIkn8+jSv3Hf/xHFoslEolSqZRYLD45OVGr1ZDVdrtdEolEEASXy4VLJoLcm82mWCzmcDjgKFYqFQBb8XicIIi1tbXb21uYrQEMajQaIGfOZjPEO3zsYx8DVazT6bBYrGw2q1KpkLCLgA6fzxeNRtvtdjAY9Pl8IpFouVze3t6azWZk2eOzwQ9xb28Ptmw7OztwIN7d3cVEv16vR6NRUL8AKlssFirU5QjpAug6HA5hqxkKhbxeLziqUIuhcrtfTECD4/E4n89HXCCUv9PpFDhcvV4HFwsgea1Wczqda2trIFAJhcJcLge5+mw2++3f/m0Uw4eHh/hwBEF0Op3hcAjvD7hLEAQRCoXQKyJh6F58BcNI/N8wDjg/P0cakEwmA1YK7TYYuyAawUnY5XKhS7JYLAjOWq1WQAFCodDBwQHQePinr6+vX15elsvl6XSKUxLC3OFwyGAw0Mzf3NxYLBaDwRAKhYbD4cnJyd7enlwuh0IMnB9wugmCmM/nDx8+BBAALRyoQcfHx0hLgq8qpHHo1Wu1GtKP/uIv/iIWi/X7/Y9+9KMvvfSSx+O5urqi0Whoubvdrl6vx0/M5/MgROfzeSieoT3r9XoKhaLdbkN5LZVKnz59CnBRJpPd3t6CkAoJNaTlrVbr8PBQrVZ/73vfUyqVr3rVq0AnqVarQIX8fj9ik2q12traWrVahbsyjNG63S4049DdgbALlGc0GlmtVjh9g2CCl7ZcLpHOJxaLQcoElRsA3mQycblcw+FwMBhAjWk0GkHSCQQCcA+AaOCv//qvHz9+DJKo3+83mUxww0c0z3PPPYc+C6B7s9k0mUydTgcCaND/f/CDH9zc3Lz00kvve9/7QMM7Pz//2te+9lM/9VPYGkajEfI5eHkgXRE5gZDJge7c7XYxeIIPLug5UqkUPoBw3mYymYBRXS7X1dXVn/zJnwB7vr6+lsvlwOAIgoCavNlsut3u4+Nj+H09//zzIGSDEIR9KpFI4CgMUwWNRjMYDC4vL9F4UigU+NeYTCb4VAORAfbPZDJDoRDGC/+vvWKtVoP7iMFg2N7ejkaj5+fnFosF1LFsNgu/w8lkIpfLfT4f2JnhcJhOp8vlcpfLBRN6SCEvLi4AXoKBD7u9/f19RIpBfQg3NHi0iMXijY0NEolktVrhdgf/wUqlkkqlAM7BeBolD6yrASmBxm82mwGGw+0WjKPJZOJ0Ol0uV6/XCwaDMDQdj8eYm6LMwQFxXwWAQYgemEwmw8EBczsKhYK5UqlUgpga3zfsMCEwJZFIMLCAmS0kxVwuF47t0AFFIhF42E6nU7lcjivCYrEcHR3B6pXH40G/AAk5PHuAUo/HY6x4h8Pxpje96ctf/vKP/diP0Wg0oVC4vb397W9/OxKJ/PM//3M2m/X7/UqlUi6Xf+ADHwAlDHEW9Xo9Ho+3Wi14BeJFIfMBJuPIT8CJBoQf+bLIu8AcHdoHMLjn83m9XhcIBGKxuN/vQxHL5/PVajUs59RqNRA7rVaLWhVTJ/wUXGVQ+mB8A2P01Wr1yU9+EpH0OHDB3VpbW8MlXywWhUIhnLsx2bm6umKxWJVKBXETw+EQlREuUpxNGxsbhULB7/cjcGtzc7NeryMSGDYlwWAQrE0wWdVqdSKRwDXb7Xa9Xq9IJPqJn/iJH//xH282m9/4xjdcLtdP//RPY0yGAiSfz6MqqVar4XAYYB+bzYbjIUEQ0GTBvrfT6SiVSvxoHo+Hi5DL5cLDEXczsrh+8id/8gMf+EAqlSqXy6VSCeu/2+2WSiWQg6fTKZ/Ph3DM6XQCjxAIBCaTCVpWg8GA0V6r1UqlUm63e319PZVKkclkTBLAKIUvV71eN5vNxWJxMpmk02kSiXR1dZXP5z0ej1ar9Xq95HsOJULAptMp3G5hZosoQNRyZrMZzEU+nw9ZCqpBgiCCwSDG9YvFAvazq9UK8RnNZhN7qdFoXFxcoLwnCAJUBTabjSodDGsSifRzP/dziAW4vLwEkXF/fx99k16vh5H86ekpxCkY9MDWJZVK5XI5mAkBWQwGg/DLAlsWMzaPx+P3+1ElAs26vb0ViUSRSAQitNvbW1xBEJtkMpn19XXEkcHUAI4Pp6en0+kUkhD4m2HVfuQjHxmNRnK5/H/9r/+lUCiePn0K9qrT6ZTL5X6/Hxf7fD7f2tpCtijGpXw+v1qtNhoNcPgQvLC/vw9w9+zsDB9GoVB0u12fzwdqr8vl+p3f+Z3Pfe5zCI6DSwVI06973eve9773ISMO5NpyuWwymWDhYTKZZrPZfWxXo9EQiUT4dcrlMqIYzWZzKpWKx+NwabHZbKFQCEjzaDSC6zKTyYRJNW62m5sbuBzAJ6XRaAiFQigJ5vN5MBiE4tHr9UajUfzDQCBgNBpfeeUVnU6H1E6j0fid73wHlPDBYPCzP/uzMpmMRqOhOoaNCJfLhdwG2i2okOBEDVnmeDzGQA1OaGD3p9Ppvb29f/7nf97f30dopkgkWltbk0qlBEFAaNPv99G/MBgMODnFYrFCoYDUTqDLOp3uX//rf311dfW+973vyZMnSqXye9/7HhY2OKxUKnVtbe25556DOSiQV+hrDAYDmnFgnNVqFY3weDxGJHu/31coFJhCwirlVa96ldlsfvz4MYJEwAZCdCmGbpAg0ul0h8OBfAyDwVAul+HnglTAXC5Hp9Ohe+RwOOl0+vz8HDGa95mb19fXa2tryL4FOLpardBMPHz4EFQMnU73DBx99jx7nj3//zzUq6srOGVubW3B2X21WqHTZrFYCKHJZrOgvpNIpHw+j/IBlTbKGFDuMMSC6xlkf4eHh/l8PpVKIeGy1+u98sorUF5gPH5+fo7CJJ/PRyIRGHyGQiEGg7GxsXGPv4LBSRDEYDBgMBiIuhGLxVKpdDAY3NzcQOcyHA4LhQK8uc7PzxeLBaScqVQK8VMAh1BYsVis8/NzkAsvLy9ZLJZYLEYRJJVK4TMGg7ZwOEwQBI5km83m9/sfP34Mom2lUsGIPpVK6XQ6giDK5TLQIviD4q/FKEupVOp0ulQqhWRjOETSaDRYWoGGizIHyiKYGCBTzmQyZTIZmF/DbPHy8hIc1rOzM51O96lPferk5AQADSJLkHexubl5c3Mzn8+ZTCYQB5BWwKRcLBZsNhvUWzD37k2xECHscDii0SjMcs7OzjweD942i8WCwbrFYrm+vv6xH/sxj8eD5AoErCAADd78iF9AuVoqleh0OkEQyMFDCdlsNh88eABi7nw+Pzo6arVaSP1AwI3RaJRKpdVqVS6XI/YNZRcM9BEMvL6+DnZMt9uFuAm8zJOTk+3t7cvLS7y329vb17/+9YieXa1W8Xj88ePH8Dq4u7sbj8cAhtvtts1mczgc19fXCJEvFos2m20wGCAxF7ZjpVIpGAwmEgmFQtFsNiORCIya4M0FLTIk1DqdDgEO4/HY6XTCxAyuS9BtIi3B5/O53W5Ab/iuDQbDZDKB/rDb7QKSwxwAgEgsFkPMGPKrULlAkAbwDvo9giD4fD6bzb67u+v1enCB6nQ6Xq83kUgAEHj06BFoh6VSyWAwgCtkNBo7nU673VYoFEiuEQqFZA6Hg6Z0NBp5vV7oYhE/AT6MUCh0uVzRaBSa13v3tAcPHsA9DdKvcDiMsh8ODqvVCilNfD4fdJdwOAzuClCVTCZDIpHuaa02m21tbQ3fOoYXp6eni8Xi5uYG67hSqUDxCYEWPCZhaQvzAjgXIAkZ9gdutxuQp8Ph0Ov1gNP3/n/tvUlsY3t6nn84iuIgcRJniqI4iZREzVNV3dv2wkiQRQMOkCCLBEkHaARwEDuOJ8Aw4CB2Gx10YLcHwDZsA4E314ZhwAGy8cKL3KpSleaBEieJEsWZEkeJpCRSJP+Lx9a+/+viqu2urhKpw3N+3/u97/OurUGX7fV6zHhGo5ESmkqlgtWiUCgA+/X5fKDG8MwBN6MBuFwuJ5NJouIGg0Gv1z8/P7OL4WejRshqtdpsNuy6CPVEWl0u187ODv06TBmFQiGbzTKn4MdHWWDI4ksFYxW0L2ZWpVLJroTPLZVKYR6bmZnJZDIrKysYJaAZIE5T6rWwsDA/Pz8yMoLxlO+YXq9fX19HrltcXOTmJQgCjwHonq9f+3g8Pj09nUql5ubmZmZmmL+azaZIJFIqlSzUIA/zwcLXJwzK9cfCAnTF+fk5oQTw141Go9VqbW5uymQynmHMpOQGcTSNjY3hwkDLpBkAyyyE5EKhALgMK/0v/uIv/vCHP3S5XDQ8C4LAER1ve6fTsVgsW1tbMBBxpmIkFQQB0CxPMpAohULhZ3/2Z3/2Z39WKpW+e/eu0Wj85V/+JVQRNmusFKRSaa/XozgVdznJTLZ7tDpzjclkMjQ4+JL39/fUhne7XdgId3d3VMZ7PJ69vb16vc4zAD0RLgnWOwzfgiBcXl6SIUA1Y2YPhUJKpZISPzYPUqmUB3Mmk0kkElBEsdJIJBKAZuVyeX9//7XsUmy1WlGGnp6etre3W63W6uoqiQOaUF9BBhxA6OMbGRnBIOz3+7F4B4NBlmc84cfGxlibgWlQKBRqtVqlUkEMNJlM5XIZ0+fFxcXLy0u1Wv3BD37w27/92/hEB4MBLmM2AvDRer0elT82mw3r7nA4fPPmDe4mkjLv3r1zu93wCok8wnAXBCEWiyUSCWrT8RRVKhVqQXhUEo3HUPTw8EDu2Ofzdbtdi8VCJTqWHo/H0+12sf3CwtXpdCjqT09P/+N//A98qL/+67/Og5cNNEs1YiMcnSQSCalcGDkocPf393d3d+FwuFAoAP5wu92CIAC5pfKLBzUnDraGVK7zVGHfTDiYdiuLxZLP5xGMGfV3d3eZYHU6HZDrdrtdrVZLpRJLjVclyGq1cnvCR49snMlkwGfo9fqf/umfRoGDC2m1WpPJJMKwzWYDUk3IcmFh4fb2FggrmVT8mqlUisuD7Mxf/dVfmUwmtVodjUb/3b/7d69BDMxRZrMZVCKfOWuajY0NyMPoHdxqEV9LpRLU23K5XC6X/+f//J/z8/MWi0WlUuEnfnx8vLi4AJ56fX0NCJ67PEBcbog+n4/tNdRyzqpff/31f/kv/yUWi4Gb/Zu/+RtqaGFQCP/UNYd6ihiBd4veGf6z2+3O5/Oc4KABQJzC4NtsNo+Ojkjc1Gq1tbU1nHIulysSiUAS4QA4PT0tEolarRZ9TnzJwYC+vLyMjo5eXV0lEonz8/Pn5+darUY1l0wmQ4/nol1aWup0OnA5lUolNaw4wu12+9nZGUgHcTqdzmaziUSCJBIIeZfLtbKy8vj4CKkc06ROp8OGeH9/H4lE2N1CSM9kMjwEKM6Fcy8IwtraGlVP9MdBpuR5Mj8/zy4K0UWlUkWjUYSidDptsVhY1hYKhWQyCSgBHQi+PmStN2/efPjw4enpiTIrk8mUSqXYY7NDAluEystuAi9JOp1G/lQqla1W6/DwsFqtnp+fQzOenp6GSCoWi2kkZmUwPT1dLBapL6QX0mQyAchHD5PJZOQU9Ho9pKJ4PH55eelwOCgQ5faBUt3r9QKBwMXFBU10uVwOMZwFh1wudzqdvM18Po/ErdfrKWTN5/MgJDnE1uv1r776ioVxu90mYzI7O8u+E0cMa3Ls4aFQCAwnaHxyGWzNcrnc/v5+IBAIhUIMcSqVik8AnS8UCvn9fvyI+XzebDYfHBzw+bx588bj8bTb7bm5OdyxFxcXnFtBK7CqhL0Sj8c9Ho9KpZJKpYwtmUzmf//v//3nf/7nXPrVavW73/0u5nSRSCSTyWAgYwOx2+1qtRpvNdMKmy9OW/SHLS4uAjpA2fX5fJiM5HI5QBaeHAQpcQ/zwON+nU6nx8fHd3Z2vF4vdk96ITihQJzY399vtVp2u53ePGyjfCnYFk9OTl5dXfn9/kgk8vz8HI/Hj46O7u/vKXtnTyGXy1dXV8vlMriWu7s7VoqU3cGF4DeFhkr/Li4b8hCxWAzS9fLy8v39vdPppGSDxWWtVmM9yoZLKpUKggC8jvUCOf3Z2dloNPr58+dAICASiYLBYCKRKJfLkUhkZWVFq9VmMpnR0dG3b99OT0+LFxYWsLXyN/J06vf78Xh8dnYW+tjd3R121KmpKXBmKpWK8y0e1XA4DOscTLlMJiPzxoEKBBY15a1WK51Ox+PxVCpFDwuOBqYMvV6PLwO4S6PRWF5enp6etlqtTqeT6CE6QqVSYc0GCxs6LpEhkDng89n+kj6gBq1er0ejUS50Ip5EM0dHR4fDIfMUKzGDwQBWQyQSsWb/9OkT1gkO8NwjlErl/Pw8S2veeLvdhjksCMLs7Gy5XMY582r1oc5Sr9dns1mG3nq9zomJ6eP+/r5UKpFQAmIEKByaniAIbrdbJBLh+aGJvtPp8ItAqqCaRCaTGQwGgOlGo5HJ4vXoPjk5KRKJUqlUt9sNh8MESRE+CEY/Pj6urKxkMhmc1Ixs+Xy+1Wrh/hAEATUEeEetVgPXRtoNm9ba2hqOMsZVhUJBIwcFOlQuw5T88Y9//Fd/9Vc2m40s9fe+972vvvqKrgaJRML2B/C/Wq1m9FMoFNAPSeVxsZGmXVxcfP/+vUqlWl1dlUgkEEDYIsE602q1LIyY48xm86dPnzj8isVijp8KhQIWIQoOjy6KCHBekS0g4AfWUKPRkGHjGna73fC+iA7RVssd0+l0ptPpnZ2ddDpNf4DZbN7d3RUEIZVKfec739na2iqXy2ziHh8fuXcj50Fmxk3v8XhmZ2dfw3JUCIEOwnHDNwKBxmQyMXdzcKPJgKa7aDRKXbbJZMrn87lcjq8Jp36n07m6usqh4+bm5stW5cvry+vL6yd+iWhbgXFKtBTq4cTExOnpKXepQqEQDAavr68dDgdi8msPKP2jaIHUapZKJQoN6Itm2qQbIpvNIo74/f6DgwO2DK1W6/r62u1263S6eDz+/PxMwSc/z9PTE91ow+GQhyT/7ocPHzDJeL1ejJUajeb29tbv94PeJYZEvIofAw5IoVDodrss5NG9MpmMwWBQq9WxWMzlcsHCo6SbXcPBwcH6+vrx8bHf76/VajABSQch+Gm1WizwRNomJyd3d3f/+I//WCaTffe73yW8C8UTVYy1y2Aw8Hg8ZOo4lCHxNptNfDRYic/OzhwOByNVv9/nrUHloskdyTafz3u93uFwyKlKo9FQXnV+fg7HgdyUyWRiYbS/vz83N1cqlaD+j42NVatVqr+TyeTs7CwRL6fTeXR0BNUKdxbHokql8gd/8AdTU1PPz8/BYPC73/0umAZs6X6/X6FQAH+k+IPsNdmF+/v7lZWVTqfT6XRkMtk333wTi8UQ2iBRq1Sq8fHxn/7pnw4Gg1KplNwnzuM3b95ATkmn016vVyQSHR8fE6Ln1Ay3gekYKuL9/b1KpWIBVyqV3r17F4lEgsEgVqDX6RLRAc/l1NQUSQW0iUajQTkDVyC26Varxb4PyMje3t6HDx+63e5//a//dXJyUqlUglNIpVJWq/Xo6Iij5dTUFL6eu7u7zc3NSCTicDh0Oh3uu0QiAeaTZPnv/d7vcTXabDb8PsPhEANhIpEYDocrKytHR0dbW1v0toyPj2u1Wpiv7EONRuP29jYUBTRmm812fn4OFPLTp08SiYRD09jYGE5Zjq6CINBBwzVTKBTw8lerVQ7dYgztnPbBsb68vHBy1mg0yWRybGxsfX29Xq/zc0OR7HQ6ECIpr0ejikQijOiFQsHpdHIw0+v1MzMzOzs7OC9xtp6cnLx9+5aiPUbudDpdKBS8Xq/f72dL9KqrTU9PszzDH0X1jtvtZr2CS9rn88EEikajMzMzDofD4/GUy2VWG8BEWUnY7Xafz+f1esFkE+hA7VtYWGCCxYhdqVTIZeM+An0M1Ht/f79arVLsZjabrVYrOWCkbBaWt7e3IyMjUqk0HA5PTU2VSiX8RdwBwbhGo1GK8uLxODnOWq0GNY/NN38J/1Wj0eAPo4a+ffsWZevx8ZE/Rh0cuNNMJoMsDR0emf3+/j6RSBBUh/AKP45j8Pj4+NXV1e3trVarJakplUpxIt7f31utVs69rNX/5m/+plarRaNR/ivu3fyu8Wifn5/7fD6K+4gycWgfGRkJBoOAznu93s///M//wz/8A1jAXq+3vb2NDPH9738f0VoikaRSKSoj4a1yHfKLwM/KvvDw8PD+/n5kZASQChl2tVrt9/tRCi0Wy/r6+vn5eaPROD4+TiQSUMKZlQA+wqoh0CyVSufn55lneeDl8/nPnz8Tw2m321arFQVHJBJ973vf+93f/d3f//3f599F/SE6cH19TWQeo8DY2Bhcgv39fYLaeMyAiWMM5dtEjvni4mJ8fNxms1Wr1efnZ+yIGxsb4XAYRhR4qlar1ev1IpHIcDjc3d3FvUq/zPPzM+rvcDiEuIFMhuNREISLiwuM5+12m3fEgwqrNGU9Nzc3dBIuLCy43W5RoVBAgyHFLJfLl5eXm82m0Wh8enqq1WparZYVkdlspuIUihe6IGQNnALk/HFqowsqFIr5+XkcyjDTyWULgsD1JwhCuVymbm9vb290dHRubg7aLZYHjUaTz+dfXl4YvyGGWCyWbDbL2eTk5ATLIDAIcj6MuxMTEyQOqOQDwEm/FiW4KFi04w2HQ2bpTCZD9oxKMQhGOp3u8+fPIyMj4XD48vIS2RgAj0ajUSqVgMIQq4EP1Gq1f/tv/61Kpfrxj39cKBTokep0OsiTdrudf5FfqiAIo6OjHo/n6uoK9y3hN6vVykGj0+nMzs6y8G6324FAAOkB3EG1WgVBik+X1cD+/r5Op+O9c0uCK2O325mBVSoVuSyZTEb2BL3d4/GIRCKNRtNsNiuVSiAQgJTP/N9sNtfX17/55pvz83Ma+X7wgx/AcKFw8OHhwePxgIMCyQOWJpvNIqmAxvrrv/5rrDrtdvvi4iIcDkcikZ/7uZ9bXFycnp5OJpN4TDhv8mkA0UGkBPmF+YhiCswyBOctFgsw93Q6TSNnIpGw2WzQvbHJ3N3dhUIhUppGozGVSrnd7lcEiUql4q4H+7tQKFDmyD1lZGQEmwxnyXg8TmqBTxjFLRAIUFc4OTmZSCQWFhYSiQRYXxr5AoGARCI5OTkJh8OVSgUTit/vR42WyWR/+Zd/yUrlP/2n/+T1erVaLYWb7ECJz4AK5vgDtg4Ii1QqxRLK8ng4HFI3x/E/m81arVaOWkCCWV8QUp2YmJBKpbiQLRbLyckJqg1LcX7RooODA2q+KpXKa3soI4Pf7ycyRDHyK+NLLpdbLBbcqbFYzGaz4QkpFArUCPINlEql+Xyeb2+9Xvf5fKVSicO8x+O5ubnp9Xperxf8b6/XYw9CmOrs7Gx5eTkSiSwuLgqC0O124/H4xMQEzY9Op5PVEUbsQCDAFTY6OppIJAhQ8K+jWjF/oc7yhWGFxAJCEISnpyc6bnd2dqAB6/X6Xq8Xi8Wg6SMpHR0dQfFC2KOr0ePxJJNJDCDxeNzhcLDQBUfGNpS1Dttyt9sNHIQEAeW1WM4lEgkrd24T1WqVTjw4jECGWq0Wi8mnp6dYLKbRaBBKR0ZGjo+Pu93ud77zHUB73KdcLtf5+TndKwjeGF4CgUCpVCK7yLcFxwTFN7Va7eXlhZ0UyrfRaLy5uaHxJJ/PRyKRTCbT7/dDoRAMauwAZrOZcGoul+OLZzKZbDbb+/fv/+AP/iAUClFB+Pj4SKb29PQUH9TMzMzi4iINdfRO0JBweHi4srKSTCa73S5nDfJdfAfIHNdqNXafExMT8Xj81c9OyI3vGEEqoCRMWHq9XiKR7O/vY8BpNptYvDjbQuiAa83ik8AB0jXGMzYpY2NjZ2dnvV4PKP/IyAiFMjzV8FKycKQi0+l0ci6+urrikclx22AwuN3ueDyu0+kQsP/sz/6MVdHMzMy/+Bf/Ym1tjSECDN3a2tq33367vr5O/57f72fNLxKJKIgmYu71epGrzWYzpDIY47j+8vm8w+EQBIEPkJ7KZrO5sbHBRJbP5weDgUKh0Gg0l5eXarWaSeqLOPrl9eX15fUTv0SFQoFqNRbLJJ2y2SzmImb+fr9vsVju7+/dbncsFoPjRPQQSiB8J4hPnOIgU83OzpIZYy3MwZXVHUKa0+lk9FIqldfX10C3VldXDw8PwVuRsXl9IIA4ZWam6vLg4OB1N0n8bGpq6u7ujqH97Oys3++zXVMqlTTFHh4eWq1W9q9s3bGc3d7evn379pUNAUeL5PJgMCgWixRzmEwms9m8t7dHdwzTGeoRlACC7VNTUxi9OXD5/f7d3d3Z2VlYIRwZisVis9lkVEbAQ0vT6XRge6nVQI8cHx+nkI2sWjKZpPh+bW0tFos9PDzMzc2BL8Agh8JXr9cXFxeLxeLV1RUVCoIg7OzsbG5uHhwc4EZlk81TFDgwHSIulwvjADj/ZrO5ubkJ5Pnz58+fP38WBGEwGBBlphU5lUqBgMvlcoPB4Ouvv8YJhuUXWzf4Eiq1qFxi5Xl8fExTCZi4mZmZi4sL2vw45oC302q1TCUc/iF35PN50p/X19c+nw9Z5+7uzmKx4Dc7ODjo9/uM2/1+H9sb9M3BYPDaLvT09BQOhyHUoFAAMSZch48R+RBk9/b2NjbFfD7v8/kgzvJLLBQK6+vrV1dXz8/P6NxsD/gclEoldrtgMFitVicmJo6OjhjPZ2ZmMGt98803u7u7GxsbzWbzv/23/8Z3ZGJiAo8J4gAzNfUxj4+PxWIRVeLl5YVVAG4pnU73/PxMxy10O94C/heufxBqNzc3ExMTFoslGo3yZy4uLtxuN65xQsnFYlGErOVwOEj4UmONpM+hxWAwkLuHJDgyMoJhHg1/d3cX+2az2aQVYmRkBIFdq9VSoQLX8+bmRqlUms1m6pRqtRppZQwONOi6XK5UKkX+TxAENjKQhNbW1hCKi8WizWbj3oSYwiEK3ZjFB/Uzg8Egk8lgHKDMIpPJIMh7PJ7Dw0OuewT2SCTCAZjClMvLS2oln5+fgc1y8mdyHgwGFxcXVOywkni1LfB3Pj8/u1yuw8NDkguVSkWv1+PAM5vNGo1mZ2dnfn7+8PCQDdTy8jISgM1mo4b+5eUFTRvvOZW/aPvcBz99+kRGeXJyUqvVwviWSCQ0WkHoxZhTqVRYTDQajcXFxcFgANsCpwZxWx4eGA1zuRzDZrPZBKLJnY62N5/PVygU/vN//s/UoDQaDYPBgB6JAi0IAgRQDBQfP37EuUyp4svLy/r6usPhYER3u93j4+MnJycymQw5HOe43W4HaELgqFQqMZCPjo7W63WDwaDT6SjTASnInAX/FR8wwe5EIiGRSPh7SOLS44MMCa8YcAaCMfe7ubm5drs9OjqaTqelUinNniRZIXtDjcpkMmwAAXrjeZufn9/f35+dneW9eL1elkrkWah0gtTPW+73+5FIxO12E+TP5/OY7ulh+fu//3tQW1999dX3v//9WCy2ubmJGkICmG7tUChEzQDKLkxMbgQ8zvv9fiwWw1cil8sHg0EqldrY2MDFMzMzg2lVLBbncrnJyUkAlLOzs/F4nIfc6urq58+f3717JwjCycmJGFxwq9XK5XInJyfoz41Gw2g0joyM2O12HIqYtZ6ensAT2Gw2gkBbW1vhcPjs7Oz09JQ1hEwm0+l0FotFLBbXajWWu1DFxsbGcrncw8ODwWCYnp4eDAborLlcLhAIiMViioX8fj9sLu4F4HbwU46OjjJ50gi7vLzMb4USQG6TFosFsR3tli9ev99vtVoGg4Er4+joaHJyEq2Lh/nm5ma9Xl9ZWQFkQDSLPDJe5s+fP4MnIDYOsbZYLK6srKjVapymiFLZbNZsNqfT6fX1dcJCoVAoGAym02l89JFIZGlpiapOwNx7e3u0FjabzVgsNjEx8fz8DEzAZDKtra3JZDK/3w+6lhCARCLhHMfhiGvx+vqaSDXDM6czOBeCICCw5/N59GYWCqhiJpMpHA6rVCqTyUQMnK6sx8dH7LYmk4kH1+XlJR7/YDDIp80Sh00tcGlur4lEYnd3F6XsT//0T//7f//vP/dzP/erv/qr//7f/3uVSjU/P/8awPd6vYjQHNAymUwmk9nY2NDr9TzoqtXq1tYWQTK3253JZHDEVSoVQRBo3qTVGMsyYSKUwmq1ur29fXl5WalUZmZmbDabxWKBVwImBxgPAZNXSAo9tYPBAGO70WjMZrMajWYwGLDLQKSrVqt4w1H02Q/Ozs6Oj49brVaPx0OpGJEzp9OJMkLEJpVKsW1tt9uxWAyR/urqigcb7X/9fr9SqUxNTcEH7ff7nz594pSEzP/09LS+vk464enpiRAg9zv+mFgsBkewsLBAlytfjZWVFSpfMeP7fD72VqiHExMTc3NzjUZjOBwGAoGZmZl8Po+MCoBKCiBDJpOxbIvFYsiqcrkcvwCLNIQ0+Gh8r9Lp9P39PUx6k8kkk8kQYyjX4x8gYMLNm4+MuzUxO6vVCtApm81y8CFcSLhzZGSE7kJo+tRNPj09BQIBmK7EBK1Waz6fxwgIdow7i1wux+dPooRw1+TkpMlkYgUViURgmqVSKYKVgiDc3Nzk8/mtrS2SiIIgOBwOQBictzc3N6FFs+nwer2xWIz4EBVwsLORLUulklwuxywMShvGwdjY2OXlJRTc6+vrpaWl7e1tmj5jsdjbt2+vr6+73S5AMJvNxhPp6OhobW3t4OCAFrWVlRXGQEEQSC52Oh2eqzz/IfFmMhnO/y8vL2q1mm/mYDBg9iRQR2cX3sdyucxZD5EYWZQ1HksfvV5/fn7+d3/3d/V6HXwZhyaNRnN4eAgwgiULj6zDw8ORkZHz83M8/larle0mTPyvv/6amN/l5aXX631+fp6enj4/PycPXavVBoMBsZRKpUIBcDQanZ+fZ9FD0CuZTHLfYSohkYwX2WQysS7EE8ll2Wg0MHpTCYpmjyNpbW2tVCoZjcaDg4Pl5WWFQgHCampq6vr6mtSvVqsdGRnBvs2zB6UcYIdarR4ZGUGvpa2GxKogCNxGmcKoYmP6Hh8fR5bW6XQKhYKu0snJSaPRiE8/nU7/s3/2z0iODAYDUguk8hCYEd0pQBkfHyc/RWmp0+m8vLzUaDRMAHwTn56e2H+Bm+NuyAAxMjLySl2EDQ6xje9CtVoViUTj4+NfxNEvry+vL6+f+CUmFoUNcX5+Hra60+mEVsAtfGdnhwwFZMBkMlmtViUSiV6vv76+/vbbb6moZfpotVqv3as8+qanp/E+8nAAOMoyf2xsLJ1OY7yfnZ1F6cFxwPbRbDYvLi5SfRKPx3GCMp4Ui0Xam8GaEuuEcHtxcVGv13FA0cpzdna2traWTqcjkUiv1zs8PKTGiqN4Npt9enqam5vDV3Z5eWm324m0UUlLPjUQCFB48/T0BOCAJR+Kz8nJiVarhfXKkcRkMrECRNPikd7r9dhvqVQqvInb29tv3rzBIwumTCqVTk1NLS4ujo+PZ7NZUCO4OQOBQDabpVV0dHSUwOvc3NzFxQW8WA6ApKS1Wi2twL1e7+7uLpPJXF1dIUnwk/DMJK1DQiGZTIbD4X6/v7e3h4eC59Xm5qZSqYT0PzU1xcGEPeLNzU2r1frw4cNrrwftykAAbTabz+cj2QylCiOTSqUKhUIfP368vb2FOQYxEIHDarXu7++DwDo5OTGZTGS9dDrd2D+90MVIptpstkqlgj5NbRgVM8/Pz0dHRwCxc7nczc0NRqajoyMqpo6Ojhh7iXESbGMGR2sg34EfDBVZJpN9/vyZLS/GFhguSqUSLSwWi5VKJWx7b9++RXf3eDzgNUnNgPK+vb1Np9Ptdvurr766uLj48OHD2NgYuk+5XOZQxoGL3hbqNfx+v1qt3traQkAg0KTRaIhEi0SiQqHg8XhGR0eVSiUjBsaz29vbRCIB7ZXyDZfLpdPptFotlkWYbJeXl5hQxWIxV75SqYQ7qdVqVSpVpVKRikQizDC5XE4ul0NwGAwGZ2dnfJTD4XBhYYGPyWQyXV5e2mw2sneVSoUhRafT3d7eUhKVyWTQbwBn09D1/PwMRvXrr78+OTkB0zI6Onp4eGixWDjYM2yTKwe92Wq1cB+8vLwwchMYx3mtUqkuLi4o6eKq4j5is9nIy9GV3e/33W731NTUzs7O+Pg4twNKa0hMArN1Op3ffvsta2qj0UjJoMViYXzF+ScIAtAKwmxQLQeDwebm5s3NDfjcTqdjNBrR5yDxtFqthYUF1hNer5dfA8J+o9Gw2WzFYhE5TaVS4UAplUpwXzj5Ew5+eHjAziuXy4+Ojt68eRONRvFueDweStspiHe5XOxuFApFIpGYm5vb3d0NBAIymYz2SaVSiVsEtyJ9hRhzbm9vmby0Wi2cGxREGrmNRiPbFkEQzs/PV1ZWoGlvbW1ZrVZU4UajQeVVrVZD5eW6j0aj6NCYO1FMeDIxYNL0wT29Vqutrq7SazMcDlnHKBQK6k6wtwCJUKlUCoXi+voa1A1JZXLiHz58WFpawujJVI6QDIPm7OwMPLparUYbxmdBdR7D3dLSErYXgqf1en1ubo51DNCKcrm8uLh4eHgIDPk1nA1Gs16vf/vttyKRyOPxFAoFsVgcjUZ9Pl82m8Vg0ul0BoOB1WplQfny8hKLxRYXF/v9/vn5OWkGkAIvLy/MrdFoFAvlYDCw2WzcTYiGo+X5fL5cLhePx2lERfcxmUyQShcXFy8uLprNJk/QbreLXQ2IPFTnqakpvoDNZpOOm2q1ik+Hm8D4+LiIRyj/2Ng/tcZfXV1R4MiKhFptrGYkLPARQ6l9xfOPjIxgE+ZbR2Tg/v7e5/PBhqxUKiMjI6urq8lk0u/3IwJ9++237969+/jxo9VqZYiC5qrX61lSIMFgiHa73QcHBz6fj+oGfuvcgN69e3d0dIRSgOM4nU6Td85kMqBo3r17l8vlHh8fm81mMBgka+x2u8/Ozt6+fSsSic7OzhCrJRIJOAPczVarlRY4BGq73U5yAVP51NQUwyGIh6Ojo7GxMVaSdKwRHvd4PLBC4/E4lnPEIyynoVAIEZr/IXWcEomE5iHM0djPudBRr5Gv1Go1Lsajo6O3b99+/PgRVs309PTp6anNZmP9Vq1WWbWcnp7K5XLyR2ydsEWdnJxQAYfYSZ641+s5nU5uu8Cv2SX1+33uwna7PZlMvnnzhhL55+dnoNOZTKbZbN7f34dCIZY4q6ur7OyKxSJQaG4uhMp1Ot3Nzc3LywvfFmzmzWYT+Ya1K95KdESue5fLRft8s9lkwbG+vh6JRKi2y+fz3EArlQqhDLFYfHd3B6kUKrrL5To5OZmcnOSOBl+H56rH40Gdvb295QsMPAXOM+oAaVS9Xj86OgroDLY2aJJEIgHmhz7Qu7s7DgiFQoG13e3tLYoMFu1yuXx/f7+xsSESiX7rt37r9PS01+s9Pj7+x//4H7/++mv2x6zeOPOyvP+lX/qls7MzTqDs/nw+3+XlpUgk+uabb46PjxcWFkqlElx1BGyxWMzxii37y8sL9VdAIWDoDIdDjUazu7vr8/lub2/tdvvV1VUwGCyVSiKWf6B6zGbz9fU1C4VutwusTSwWh8Ph9+/fr6+vk+pxOBwnJyc0dHHOZ0HL2RhH2vb2tsVioVMLKhlakd1up36Nh7ZOp/vw4cPKygpZO278nU5nbm6OSBjELbB9YN0EQej3++12m4AZfxu/fmwa3PU5I2CviEajg8GAxR7bu2AwSNjBaDSi+HKYv7u741xdKpX0ej2H6mAwKAhCNpu9u7tbX1+HcM+qXyKR2Gy2fD6Pikb05unpidslFXPslfAXttttguoIThgT3G7358+f8SxSn0F2i3uHIAjsMpRKpV6vZ0IpFAp4AavVajgcJrOHPud0Omn9ZFvBjclkMqlUqng8Pjc3t7e3FwgEotGoWCxmhLy7u2s2m6urq91uNxqNkj/AwyKRSFgMXVxc8AiCcmy322FAPD4+KhQKqmH6/f76+rpCoWBC5FkNUYIOPUEQ0N7gAHAu48N0uVwsF3EorKyscMvGUtnpdMxmMyF6oETFYhGHLktE3CJSqfR1DcFTAdWwWCyygFhYWIjFYtgTOA212+13796JRKLt7W2z2WyxWE5PT7VaLYh/nU6nUqkYrrFIMKyp1epXJVgsFr86FaAog3q8vb1lc5dOp2mNMxgMe3t7m5ubWAqYRLgpg0qjsOL73//+3d2d0Wg0Go2np6f8QsPhMGmvs7Mzrt67uzuaKMCpkcohC3J3d4f/IhAI/Ot//a9XV1fZ9z09PWFgKRQKLIM4uRuNRr4p/Kjn5+cUgIyPj9MWTDoBXh8Jki/i6JfXl9eX10/8+keeGkzU4XCIMFkoFIBHkapg8sHiQrsMFZ5XV1dzc3OVSgUGLKdxnFEOh4OOH51Od3V1hcO/2WyyUr25uVlYWDg8PDSbzdls1uVyUdrGcn5rayuVSnFLZoglcYfUimKHWVCtVqvVasiOcH3g601MTAiCYLPZrq+vdTqdIAigaPb39yFKkU+lMJUKLypRV1ZWSqUS9RM454xGI/Y+lUp1dXUlFos5JvC+cEawLGT8xtSgVqvJiZHWeW02UCqV9HtThQs8jtNWr9czGAxAvQKBwOHhISfYxcXFo6MjlUpFQ5Lb7dZoNIIgYEthnLm6ukITPTg48Hg8pDMx3eGO7ff7a2tr+XyeRCyjqF6vh2aM8eT6+poIIoHOyclJ/oloNAoODooSR/rT01M+ybW1NZocWP36/f7j42OkOLVazV8I+U6pVL5///6rr76q1+vI2xMTE7Cn6FvK5/Ov2ipDNRYphmJcfK/lUjc3NzKZjMM/226cXbhpAN4BQAUIHAgECFVardbd3d3l5WV+WVTMZjKZ+/v7mZmZm5sbhUKBpmi32+nxisfjZrO51+s5HI5yucy1EQwGgQk3Gg3sxRh8379/T84dCKjZbH4t6JDJZD/4wQ/AkTqdToIt6IBXV1culwtvaLfbnZ+fLxQKRBO1Wi1RTAhvfBrT09OCILDyt1qtlEILgsAlzVURi8Xm5+d/8Rd/MR6Pz8/PC4IANwvvJUMf8Dd2DvyjNput1WqdnJywJwFCzofPop33IoI0xSnx8vKSQwuZdGpaPn78iP0DR51YLG61WslkkitGIpEcHBwsLCwkk8nn52f6eJrNJhsE0EaLi4s4VTG0cKTH1imXy0HUnZycCILg8/nIL0NDRBNFLsaUIRaLNzc3j46OSLLx9cDGxwod24jb7SawxCkLIgPfumQyaTAYEFPpueL30ev1UqkUp02NRoOPEEmSzL5EIrm+vmY24dhJ7g6JFIWSXNxwOATV+9pajmNqMBjgAkB+pjBFEISLiwvSurzHmZmZXC4Hn87r9ZbLZZFI9Pz8bLPZRkZGTk5OAMbFYjGMSeTfXpdKjUYjl8shAzebzYuLi+XlZVLCIyMj2WwW7+DDwwOd0jMzM2q1+uPHj3RB8jYx1GGCQnBhK4HX7pUh+Pz8jPk1EAjQ5wLwFfN+IpEASmo0Guv1OqqQxWKp1Wocd7HnFQqFUCiUSCQMBgO6NT8DoSz+6dHRUez8VOHs7++vra1RBgRPm+95o9HAksdsgnIJ1xu5ShAESrwRsxF3rFYr9+uFhQWlUnlwcADkERMT9qV6vc7f8Lu/+7uMmQBWGEU1Gg3MV76T5LYh0T89PS0uLhLdHh0dpeqdTGqlUqnX641GY2Zm5rUsLpPJQAOkyot0HMoxCpfX6yUpikJ8enoqCML8/HwymWQHh70FyPBv/uZvco+gKCcSiSwsLEBOrNVqZrMZTz0tSDc3N8zCXq8XWHE2m+XrQAJ4bm6uXC6D7xGdnZ1RkPXp0yemZX5WSHbktVOpFNxzSK1U2/NlbjQagUCAuRRYO65qsGhTU1PD4RDZljkwkUjg+zKZTOB/V1ZWuGharRYLYEyHjI7T09PAezUaDVxMYuNKpTIYDMbjce50ZrOZmwg35qOjo9XV1YuLC5/Pd3JygmzJ1xtLNQsjs9l8e3srCAJyDKF7s9l8fHw8OzuLME66ASTi27dvASZKpVJIojRcra2tUU1A2dpP/dRPofBTn/fq4/r06ZNYLF5eXqawC+qfVCrNZDKTk5PcCEjucmTgiQoG8eTkhCcqU3Q+n7+/v19bW2Mgv7295XmVz+d7vR7GrdHRUcRRDJobGxs7Oztms5mtE1vGbrdLK/D8/Hyj0SgUCq1Wi+MbTz8Wfu/evUskEs/PzwB7seGxIOdLwvMTM3uhUCBRenNz4/f79/f3tVrt/Pw8nams2DipwQEB/8kPg58K9XRiYqJarZJUWF5exhW2tra2s7OztLREpIWVzdnZGdl/WilYAj4/PyO+mkwm7qc8KnGsp9PpjY2NUqn0H/7Df6D/nW/X4eHh7Oxsq9USBGF8fPzz58+oLWBEEemz2WylUqGdj84NNlkszrVaLZkRjlEgQrRaLSkquh05LHNC7/V60WiUQPDKysre3p5IJFpcXHx8fDw/P9fpdLVaLRwOU0ObSCRmZmaQSylhW11d3dnZYbGQSqVCoVCpVAJB2Ov1/s//+T88mYAtEjs6Pj5m5yWVSoPB4MXFxdzc3OnpKZcNZkIeDJwGOp2OwWAAMM5vSiaTiaDm1Go1l8v16rSFx82u6JUZ7ff72bxw1+l0Om/fviXDMz8/f3R0BDeFYEI2mzUajSQFgsHg9vY2QTiYTjRi8NAmC8O2mR5AiMSY6rCZQHBsNBpnZ2eUCQIre92Qc0Lr9Xp864CPw855fn7u9XrLy8t0i7GogxYBA6rZbD48PNAVQDwEhbVYLHo8HoytNCrMzc3xhJTL5TMzM+/fv3e5XCaTSaFQXF5ems3mTCbDskan0+XzeUhcdrs9m82aTCYgkZhi2d0Ay+YWjIUR7DsHQIIYFNb2er3FxcXr6+vb29ulpSUQx4VCgQtuZmYmGo1CkSTIb7Va7XY7iF04Riy/cCVjMMWfQpgQG87nz585gAwGg2g0CvmWgtXHx0felNfr5f7i9XoVCsXZ2Rl8h9dmz16vp9FoUMdJHvAw5JaEr4fGUyhBgiAMBoNyuTwzM3N6etrpdKxWK4fTsX/qOmXF9vDwsLm5ibUcsi54i+9///vsmDhaIs0yL+AYAh1WKBQ0Gg1gOj4c8hOc5Nmvm81m7hEwwR4eHjihFAoFqiTIYa6srICJJlomCAKgdrByV1dX5XJ5aWnpFSBweHg4OTl5c3OztbUlk8lSqRRAFvbcz8/PVquVryjvlBZYvV4P3HtjY4PDHdsQ8kHT09M0VFOtAKLt/fv3kJkpdvyt3/qtDx8+sIlnuxSPxweDgd/v5zYNKm04HLKpaTQaMpmMxxKTtUajYbPLc4v6OBFsm4mJCQy2vV6v1+sdHx+zlyIVyqyLu97r9dKLAaOYFRfnKMDW2GPX1tbAtFFKms1m2dgTFuIJj9m+UCigHaytreVyOXo3/H4/IDO2v/QVOZ1Oh8Oxu7uL44MnHsu8QCAgl8uxWmNwoGhjYmKCxZ5KpQI4wsBMvI8tA01RnORxNA+Hw1wuRwyXahwODlxP2JMXFxdxW7NAZeYkFgEzlm0FGy+/3w8zgpVQJpPBC8d99uDg4OXlZWNjgxMg2GupVBqJRNbW1l5eXg4ODtbW1vhrp6enKQ0aHR19eXmZm5t7eXmBQEHbCAtsDk2CIIyOjvKox7bErvr+/p4yZDZBJDhubm4IMaOJqFQqkUg0OjrK4NBsNikWqlQqy8vLjCrpdJqBVCKRUGSTz+enpqaoFsbhYrPZBEHAcj4zM5NKpThI4pX64Q9/OBwO9/b2TCYTJndKG9GtqB3kDZIWu729JUXFfI1RgtANOovP5xsbG4Px43A4IpEIxzGmquvra7Dy7XY7HA5T7ErJUzKZxAGIQa5YLFJMz5GQ6Bf2DalUmkgkUM2azSZfvJWVFbFYvLe3t7q6Go1GrVYrIwAQFhwr2F4qlQonUIPBANEaVtPt7S2GGlIng8HA6XR++PBBoVB4PB76RkQikd/vJzb9J3/yJ3ykLK2pLtXpdF6vd39//+npaXZ2VhAENvdAN0g84C7DqQCFkDdCZg3YHaU2kPpzuZxSqSRAiO2AfdOXrcqX15fXl9dP/BIlEgkihkajEQr7u3fvSKOiSuCDVigUqVQKpQTtbTgc9vt9jUaDAEE5G6qbz+cbDAbEpVUqFewvYtFkqzGGGY1GCihh6spkMp/Pxwoa5YaJ7vj4mDQdHodGo6HVand3dw0GA5Q0isKVSmW73dbpdGg/2I1dLtdgMLi7u+NQ0+12r66uaPHGtwNlA1/j5ubm3t4exzaQR0dHR3a7HSGA522r1WKsXVlZiUajqK2ER9VqNfk6BkVUBhQvbBGcxjudDpV8oElEIpHX66U46vLykudntVq1Wq1Wq5UjAEc2GGUElBmP8X2MjIw8Pz/jTCNLzhqIQJ3H40GU4bGTSCQsFgvaMwany8tLktdYqunTIsodDodZlhF0ZEawWq0ul+vTp088ITUaza/+6q+ChuPIjU9ve3sbCiQ+a6VSmUgkHh8f6QqAsehyuYrF4tLSEv11IyMjU1NTHDcwxcGwoEPEYDA0Gg2oWQQU0Y9xYZPdogxNq9Vy4FcqlVdXV1jI2+02AUWHwwFkl8J6dDpCerhUDQbD8fGxz+fLZDIgGol4UZ2Xy+XwIjkcjsFgQKWzIAhOp1OhUBAXZijm/5PNZgkx0HxGJyZv9v/+3/+7v7/vdDonJia63S526kKhAJsiGAxKJJIf//jH0WiUgOjP/MzPwH8xm82k9Wq1GlP867EIaHar1QqFQrQdXV9fh8Nhwrt2ux1OBZH3ycnJer0OT8zhcOAq5OxzdHT01Vdf7e3t0Z0aCAQ4uUxMTIAll7pcLmKFVF2oVCpo4Jy4Hh8fZ2dn+/1+IpGgnr7dbrdaLcKgELdfJwJECq1WKwgC8TtSyRTBU6dIDIS0IkHbi4sLp9O5ubnJNMEwXCqVqKh6eXkxm82tVgvVDanl/fv3MzMzKpUKJ38kEqHpj+uGBVsymXx4eGg0Gn6/XywWm81m9qBES9D8Xi00d3d3uHcxwqONXV5eejweQRCQqSjaw/HJ4dZisUAwL5VKoK5xzRoMhkqlwrn05ubGarUWi0XW2xKJhNQm4jGHQyKehUIBzRiyAXPT+fk5pao6nW56ehqsvN1uZ0zAazw9PQ2x5ubmBoyjSqWKRCLsMk5PTyG/s8KQy+W/8zu/k0gklpeX8Ya2220608iA63S615IUsVgcCAT29/cplyoWi0iPTC4A9bBL0/iZy+VYxqHb8U1g/I7FYisrK6enp+z+aeTc29tDlGHuY7qhqhK5pFAo+P3+vb09nU4HkRzz0urqaiaTYWvG9q1arbJp4gqkhBAoP/FWrLThcJi0rt1uN5vNSNpisZi9DJQ9tEB2Lhh22W3REDw5Ocnakbvt8vLyv/k3/wamcblcpvPtzZs3nU4H3300GgWo0263Mew9Pz/zvPnw4QOe6W63K5fLS6XS2NiY3+8vFot4CElXaDQaHh6Tk5PEuNkMAt/GoxyPx7e2tlQqFWI50Y1utzs2NsaDma4yzH6IUCybeMDj/H55eeFd9/v92dnZo6OjpaWl6+trOCyv45VOp4tEIlKxWEzLMTiT1dXVl5eXZDL59u1byk3ZtqKgkF+gPOHjx4+Ua0EtxsNLIOru7o7nACI8u322HgsLCzKZjFpZsL342S8uLpiWZTIZbjbW4FSBshOZm5sDkKvT6UZGRlhD6HQ6fBwYPQg+XV1dUbwIXqzb7ZrNZlI2Op3u4uKCHjD0IUA7bL81Go3P57u6usrlcvhwCePXajVkSJyCyNrs2NiS8j2v1+tOp5MlDroGl7jdbpfL5RqNBrQ8DyIg9+TB4UHQ5Unx3/z8fK1WW1pa+pf/8l8CT767u2u326/uSUEQeHzRYEAyTRCEs7OzjY0NItWUdf7RH/0RjQSI/6h92WwWqbJWq3EAbLVaFosFbUiv17MG4nrlpoz3hL0j9oSHh4dAIIAODfyqXC7TeAqNDYgcyRRSUcVicTgcmkymk5OTra0tBLVsNgshmcw7AUvEwlQqtby8LBaLWQ+hZ/O8Zdk/OTnJQZIORIBp7HrISeCCubm5oWfb7XazVfmlX/olyoMJCmDS5VT76dMnrnkUNDz4FCkiAEHNoSkZfDxrQbqggULxxIICRa0BnwDfQ41Gw1Hr7du3+OI9Hg+9AniCdDodkh9Phb/9279ttVpnZ2d0NuOooiBqZGREoVBEIhG73b61tXVzc0NqZHNzE58B9gKn00kUo9frXVxc6HQ6gFjcPoD+8+wEguPxeAhAcBcmbAXc12AwiC4vL9kP4emC9uP3+yn4UKvVgiAYDIadnR2lUhkOh6FLnJ6eQqbR6/WDwUCtVicSCRy7aN3c2Fjh+Hy+3d1di8VCEyJ3UwDNwWBwd3cXWQt+LL9mMG1YrdiW46VhhU48FEoYLKPHx0ewEeyJ/X7/w8ODy+ViycJxgOKSwWCQTCYJKQWDwdHRUQDol5eXGB/8fv/19TV2nU6nwz3+4OBgZWUlm81Wq1UWTxzzWDQolcof/ehHP/rRj+LxeKfT+eabb6rVqtvthlHocDii0ShDFmtIlUrFMgzadbvdJvmK0lwul4vFIldYs9lEo02lUvV6nSUOR6FisYhTi1wDpXBUY7B1Oj09ZaNJEwLIoomJCco0C4XCcDh8eHgYGRmhbg7vPEOKQqEgXwswJZ1OkxiamZmpVCrsFDE40M84MzOTTCY1Gg2Fmy6Xi+/A5OSkTqcj5zI+Pp5KpXjYYOorFovw36hlwydCKS8UMv4MR0s26GBcfvjDHxIdYqxYWlqiquZ1sVKpVNRq9ePjI+OSIAgc48kovLy8vO6hSIWyuWBYo0mg2WySeWOLQWnA9PT0yckJcjtmM7fbzfNVo9Gw5+bZ8EpjfHp6ojYICjnvSy6XcyqcnZ1FLQZfzvmCj47Gkz/8wz/89OnTn/3Zn4GYSafTPp9vfHwcdxVXb7/fNxgM7G5vb285VcErIjpM/xtd9nBzYLIvLCzQ/e52uy8vL1nW0DHEeoG4STKZJPm1uLgoFoshoX8RR7+8vry+vH7il+js7IxeYuyMhE1vb2+dTifQsU+fPhkMBqPRyHler9fjO2DshMI+PT3d6XRojoEaRG3M6Ojo8fEx9fQAe+PxOMOw3++PxWIEK+fn52Fn4XspFAqvaEmmRHbARKT39vY4kXK6EwQBKOn4+DgOa5FIJJFIqK2ORCLw2hqNRr/fDwQCnz592tra+pmf+RlmB5/P9/LykslkKONGIACKQbE4QclUKoUTPxQKkfFlEYgDnRQ8qGfKdVhzrq6unp+fMy7iaif7i3cWMB/zJI9cFqJsHPF60a62u7sLchWGs9lsxkGPA+XV0gpazmg0Aj2gI8ZsNrPmNBgM3W63UCjMzs6SwhaLxXA38MvSnwQNGGSBQqEgMQVFCvbUcDgEww/XD2dgqVSy2+3xeFwqldrtdg7t79+/X15evr6+DoVCv/ALv9BqtYA/EiDEGNrtdukruri4oCeBMDECEPDXra0tMMJo2LRDWCwWguGENeVyuc1mOzo6mpqawroGzSCdTmMFUCgUDO07OztM/hKJZG9vDw7I+fm51WpFnwIsiD334uIC8Yu9JiIX1BiTyUQTOFvnw8NDJmKpVGoymahfQSR6fn4+OzvjupqYmEin04BLaZAAHzkxMVEoFCYmJiAJSyQSxvNf/uVf/t73vhcIBGCdEF+GPHJ6evru3TsquJkVnE5nIpFANReLxScnJ6D23rx5Q/iYMcJsNjNRYgMJBoNgq4fDIbFVKCdYOnu9Hj1+Go0GckKhUNDpdKLPnz9PTk5iTIJxKggC08rBwYHRaMTQxRep0+k0Gg3MAlxV6NVM2ijMQGvpVX98fOTToRTj5uYGKbFQKCwtLWG7fIUMA1/s9/vIv6VSqdvtTk1NjY+P4w1NJBLBYDASieAlk8vlrHsQ7XA3eL3ey8vLUChEchxAS6/XSyQSg8GAMAicwenp6X/1r/6VWCzmtphKpYjSR6PRra0tGt7ovANIyydLvRXSydXVFY3wHCAhoIhEoo2NDfgmS0tLnz9/npubq1arjMEGg0EqlUajUQrBGcKR65BCR0dHaYccHx9HSFtZWaHVzWAwXF1dsdjCYcEd9v7+XiKRAO8VBOH1Bkpo2Ol0FgoFNiD4mnGvI8cgLhLq+Y3f+I3f+I3fIDj/ox/9CIst6Xu0vcFgMD8/n06nmWtQgpeWlk5PT2miRE0EaXN6erq+vk4hIxZpKtcFQXiVCWu1Gja2UCiEuQAojkgkgs/c7XZxSTebTapqJiYmKFKi9wTHHVgTQRBIrLbb7cnJScgAgiBoNJrl5WX6q9DvyAdUq9VgMIg1a3R0FCIk1H6iN6CY9Ho9gXS0Q7ZLPMmYC2g7lUqlgiBMTEzEYrH19XU4VVdXVzhHMNHwGMBOigP46uoKpwnTBFlztVrNl7Fer7Og4Wtls9keHh6I0pCQQuRCSLq7u6tWqwAcUG0ikYjL5YIRW6vVBEFYXV09ODigRDGXyykUCtYI9K2wfuLQMDY29vHjR5PJ9PDwgO1Np9NlMhnuGs1mU4Rc/LrVwznPty4UCh0cHDDakZ5AcQV6Dg6LLwySAUDg+/v71dXV29tbdqXEdUFaEtPK5XI+n08kEvE0wCLNt+jm5gZFo9/vE5cIBALFYpH9olqtphGelnar1cpmlPUkyTcSU8Tz6UwkosNkix2eXcDIyAiO1V/7tV/jK/Hx40dWtvhtnE7n9vY2BYjD4XBxcfH29nZsbOzk5GR9fb1cLqMXQltJp9MikWhubg5OLG1pDw8PVNiRYUP/55KlF+v+/p62JwIvfLtUKhVko2q1GgqFQFR4PJ5YLNZut//wD/8Qt9U//+f//H/9r//FGY0rbGpqCuHq+vqanioeR2Kx2GKxoB/h+6TnYWJiAtABgmur1cLbgzpwd3dHCyw/GxDQ+/t7tiR+vx8Za2JiIpfLYXkUi8WsA1+rD6Bm1Ot1i8WiVqvHxsZomaQ6gwY5rNngOaLRqMPhGBkZoaWBIoiJiYlsNttqtWw2G6VkUNQcDgf9CWAy6OIYDod2u51DFtjOSCSCzEEMMhAI4FcGRNLtdtnZqVSqo6Mjv9/PWvD8/BzfLTp0NptlB//09MQxloco5QZarVar1Z6enrZaraWlJbakxP9heXGMUiqVqEI0aRKQ43mGkorD+ObmRhAEt9vNTpqjJZFRWLP1et1oNO7s7PDbwdTn9XpTqdT4+DinRaVSSbixXq8jafGEIJJ3fHxM/wnhxqmpKZ1Od3x8jOuUdkHGCL7vXJPBYDCRSAQCgfPzcxH4WUo3BEFIJpNEHlCSAI14PB6aLLma5XL53NwctWbU3lG8RiuHRCJRKpXU2OFlbLfb+Hm73S7So0wmW1pa4joeHx+/u7trtVoQkvP5PLw2LPB6vR4TvtlshjTd7XbptWb5QljIYrFQAU2dKlEiqVTKxp74IxEsaiXJGvCDqdXqv/iLv2Byg0MjlUpnZmZwMWq12lQqRbR0bGzMaDQ+Pj7u7+9PTk5SLk2yAHH+V37lV+r1Ok18rMpw6wORh6fAjgZfP8Ecvl1+v58UH6aPTCbDpjAajS4uLgKOdrvdoHqLxeIrtp/vDycIzCm0N1CVQLEIi+HV1VWegYDUwPDAmykWizAj8BQ8Pz9z0MvlciSptFotYR9YSi6XC58xa4jR0VHCpuBU0un0/Px8KpWi3QNDOmdpKhSwlphMJvLKlJgdHx8zqdFyRoTEYDDgJeHsUCwWFxYWwMEgxE5OTtLLje5IT0W1WiUE7HA48BBcX19jxCCTSl724uICZy0cIIvFAgARKXdychIcJBv6YrHIE06hUAB24vS6ubkplUpJtfZ6vXA4zMjDMbNWq3GRx2IxQRAajQapFnYLLpcLMD13eYyb7XabikbSWw6Hg071fr/PRQjejdwqVTJ8C1j0suvodDp2u51bs9FopJsWWCTB7pWVFUEQbm5ugHW9mtm5T2GQZyxqNpvQMKlqDgaDX8TRL68vry+v/z8vEWxhbIVSqRTfhFKppMwNzwUdLS8vL3t7e2q1+nWUZayg24r8K0c7kGrcknnoqdVqzHyYC0n7E05Fynp6eoKpp1QqcXkh5kUikZWVFSwDT09PdGK/f/9+fn4etNnx8THtPmazeWxs7OjoiL+Wci30PDqEiAx3u138PBCuXl5erFYrzVrv37/H7YfoqFAofD5ft9tNJpNEchF9WRBiNBwfH8euo9FogCao1epWq6VUKg0GA/oikIG7u7vp6Wl6ifb29pRK5fz8PJk6cJ5Q28ibYgdwuVzRaJS8D9xAiUTC6ZEOYYyqmHbu7u5MJpMgCBQyUPR7e3uLaYVZyWg04pvsdrtYISYnJy8uLhYWFkQiEWBOMjWYnRYWFp6fn0mUM5ThGObhfHl5SYkJqkS5XIa6AP8KRw8jNKTFRqNBlRznHfCrzMjMDpy58K2Sj+j1el999VUmk4HQBwsGhejl5eX29pZ5GycuETXQZwTSnU6nIAhIgNPT01xCwNNardabN2/u7u5oS9HpdK+J2F6vB5WDtoR6vV6r1Yg43t/f865pI7u5uWHlubKygrMD/jbxSwTIs7MzBgSqyKampph8W62W1WolIjQ1NUX7FHMrYK5Op4Pl7+zsjNQioRXeICkbikRwzXAqx9hNtpAMNFXQ5EVZViwuLmK8xDzGmHl3d+dwOBC2np6eqtUqbA1gAlxyeI6bzebCwoKY4R/kTCQSwZUAa1Aqlep0OoqdHx4eIpFIOByemZlhMFOpVNPT0y8vL9Fo9Pn5mfkfhR9ytFgs9vv9q6ur9EUOBoNcLsf4NDExwY/e6/XOzs5wW6jVao5n7PZPTk4wfXAlaTQaeKgcVuVy+f7+/u3t7fr6Omzby8tLPCNIpO12+/7+no8mlUox5oRCIep8A4GAxWJRKpXQvW9ubur1uiAIVqu13W4DlZyYmLi8vDw9PeXKRpfBaCSRSI6PjznCXV1dUQii0+nsdrtWq8UhilRsMBjm5uYkEgn5S2Q2v99fr9f5kCmyJ7JNkJckniAIcAxQiPGeg88LBoOAHfHktNvt4+NjPOPlcjkejyP6vFra+v0+2FHI5qTXaKvm6sxkMtvb23K5HAMIXtJQKBSNRlHgyuVyJpPRaDSQkwRBQJ9yOp2Li4sUZRMmwOZotVoRv7kFIzzRQy4IgkKhIHIWj8cp3AwGg0yI/IuBQGA4HAaDQavVGo/H0+k0QtjOzg4uCWQLTt3YPebm5rCr3NzcsBlBo8FBa7Vaz8/PX2lJCoUiFAqhYgATeSXx0JnS7XaRQg8ODri98pWTSqUQA+geQ/cNh8OISlA4YTW3Wi1mNDSybrfL1U6g/hW/jA3v9vYWEDTONJqxQNsLgmAymVwul0wmI/kej8cDgQBZ5FwuVygUut0uhaT0qvCkF4vF+FzK5XIoFELayOVyX331FUXWr3M0ZSvcdHA/g1OC8i2Xy81ms8PhCAQCXJN4RsSXl5dQrTFTI25B6yqVSiR/jUYjcybFCGBIXuvLlpaWGJWxnzudTm5DMK9Ztl1eXt7e3s7NzZVKpdc6UqvV2ul0VldXyU3c398DgBwOh9jahsMhGw22jw6Ho9PpAImIxWJ0l9/c3Hz48MFms83MzJTL5UQicXZ2hrV2dXUVG+9gMCAIiMd0enoaYCzOXKT44XBICOLVBcfSlIYrjNUA0HE0BgIBEEmcJsho7O3t5fP5w8NDEGfFYrFarXJZZLNZnt5sRkZHRz98+PD+/Xsyi8PhsNFojI+Pm81mrMpyufzDhw8EW7EzCYJA3c7u7i4q79u3b4PBoMvlYrPLHoFPldCQSqVaWFiAs4BPFBsrB4RarYavERuY2+1mYy2TyViFOp1O7lAikSgQCDw+PrbbbZbxoVCI2E6xWMS5SCCF67VcLrNHy+VylEixaNvd3f306dPp6eny8jJE/4eHB7AxGHzZgrFr6HQ65XJZqVQSgZfL5TKZzOPxwDrD2xaLxer1ejQaJbqq1Wp1Ot2rYf8VrTY+Ps4uH4Tt4+NjJBLp9/u5XA45DGgLx6Jut8sD8uXlZWJiolQq7e/vN5tNr9d7f38PiAitlKbk6+trFHrA1LzlXq93dXWFMW96enp2dhb8AhuQ12oL/k8ebzs7O/TI4TGjNkEikXCWdLlcsNcxc+P1Ylmm1+vdbjfnQbLmZrO5WCxycwEyCn5Vr9d/+vRpfn7+1VpB0P709JTKe9wYKLWZTObi4gI34NnZ2fn5OTH8h4cHi8Xyj2RdrmnOVJiIscednp7Ozc3hqoTgSt0JyYhKpUIF1tu3bwGZQrvlJCKRSCYnJ91uN2YHKBhWq5WmOaVSeXR0xDIVECtrud3dXfoBAKl7vV673f4aVNna2uJuAo+PmAPLmsfHR41G43A4tra26G08Pj6GlGG1WqnVzefzVquViA0A+Gq1urCwwBNDp9MZDAYqlI1GYzKZpPAF/q3X63W5XJisYUkJgvDw8NDtdhuNRqlUAt7FJgKYkFar5X4BQtbn84XDYZ7M4+PjYrGYEyZjDkBg1p/sKWZmZkQiEeK2wWDwer2BQAC8uMfjyWaz+/v7KpWK8BsHEwAIWMi5OjGDgt7n9rG/v88dEH83GBj6pbknIsIDQE+lUqwwKAOm4AYzy9TUlMPhMBqNgiBEo1FmEG6vnU4Hz+LS0tLZ2Rn9rGy7ucXwpQIoTVUoap9arWYoJuPz2vmALAp0kqyQVCpdWVlhr6FQKMrlMnXWBoNhd3c3HA7DScJACeOv2WzabLbz8/NWq8VfAio5lUqxXGcW4COltxGz/ObmJtdMPp+/vr7mMDIcDgEOEJKAPre+vs5pRa1Wy2QyQRD8fn82m6Xuz2g0kqukNWZiYmJra4sIXyqV8vl8ExMTMzMzPCa59ROWgQxUKpU4OAwGg52dHdJx+GhYR+7v7w8GA36PuEjy+Xwul4Nig0ccDDgXwNHREUkOp9OJIMDcx3OR1lGv14uor9PpKIthEPkijn55fXl9ef3EL7FGo5mbmxsbGyN+xgPh8PDQ5/M1m02qt4lddTqdkZERUo93d3fBYJCz3O7u7s7OTj6ff3p6wmdJ5Rc9A/l8XiwW22w2FAGAw6yLKOCq1+vn5+f0O1QqlbGxsWKxKAgCEhc7cB7LnU4nHo/rdDqWbfx4SqVyZWUFsWpkZMRoNJLn2djY8Pl8LpcLq2IoFILOFI1GuYvjqoAe7Ha78diQ9Ds9PcUAcnJyQs2Hz+fL5/PgT7a3twG6IU0xyWO5gRtWqVRQnghukXqIxWLFYhFaFPd1og1LS0tGo9FkMkWjUZLHr4XbhH1qtdrCwsLBwUE0Gt3f30f2A49K3wfmn+np6Wq1urq6iqOERpJMJkNyn8SK1+vNZrMkVl+D5ySGCRND5SdziBvCbDaTkFYqlUCGnU4nS24ev+xcCTpDAyTPZjabycjBFh4bG2NMGBsb29zcJKl8cHDAf1sqlWZmZnB/knBhUwsemU4vcswUqbdaLbabrxfh+Pj44uJipVJB9oLuxcH+6uqK6Mf8/DzEJiZBu91OPO/p6WlpaSkQCNAjgYvn4eGBBkICry8vL4iOr1zbqakpiFCg1XCIfv78mXM+Z/7R0dFyucyHT0QtmUxSQ9vr9S4vL9kSAHm8v7+Px+O09oCSgrrGmeX1nzAajZOTk5BcASNSvjc1NUWikqvOaDTm83nAaNTZYmkTi8XYIPr9/sbGBqZBKv4wH+AeJILc6XQODg445WFHOj4+Jp8lDoVCFxcXiUSi0WhgUiYDdnd3Fw6H6/W62WxGdiKuB6lYKpVCuGg2mw6Hw+PxuN1u5BnOhDCmyuVyvV6vVqs0RwSDQQCQvHMYyLOzs06nEyEHwQX8EaF10qgLCwvwvvx+P3lBRnTQbJFIpNvtAqd8fn7GMnBycgI7AGN4PB7n46D5EW4Cfxi9oF6vX19f45xZXl7GpQfo8eHhAY4DVkg8C69M9nA4zC+4VqtRWGe1Wvv9PvpoOBzGFkVz4mviSKFQkONEx4JM9/j4yMcIGwVgLFYfn8/n8XjwnrK7ubu7Yy+j1+vHxsbQyWg/oqJxenra4/HU63WEiUqlcn19vba2hsBMZDudTlNPtbGxgXkhFovR/ByPx/F6UaCDi3lqaioej+OvJW3J7uPp6YkRg1uSXq93uVwsU6CKEMTCusq3Bb+Jx+PhjdO6Ui6XCROwonI6nVKp9HVZgKOHxCOBRhiXyWSyUCgIgkA8EhCcw+FAiIHMQqR1fn5eKpXKZDLIxqxpTCYTYrxYLAaoJRKJ8FPm83nMXbi/zGYzlofFxUVGVL/fLwhCq9Xitw+mjKFpdHQUvy+62Js3bxCMUqmUIAiURYrF4uPj42QySQHN5uamIAjsBG9vb2kCBLdBBARV4vr6GnsFJkOaVQeDwfHxMU+a6+trKs2RxrnXT05OVqtV0sadTocKgZmZGUL3QBgPDw/b7XahUJDJZMfHxzKZbHFxEfcmaLiFhYV/vHEA2qCs6OrqyufzYWp8Xa+yQex0OplMplgsfv3114FAgLvdzs4Oc2M6nbZarSB2GP9cLhcq4+zsbCKRwCOcyWTS6TQteziOSX/ygHI6nQsLC36/n8atV0nCaDSenZ0R37i5uaEq1Wg0wqpGwAPSCTzVYDDkcrmJiYmbmxu898vLy6wA9Xo9xyL4oJxciMOoVCoiAxTqIGGi9bKMoAuHJxtIfkz3IpHozZs3VDq+2tvA59DhyifAWYMgZr/fx7gFuhqxTaFQzM/P39/f7+zsIEYyiOKAlslkOIh5ZKnVaofD8fnzZ8b7ZDL5+PgYDAZHRkaazebx8TGjeCaT4aclrUP01mKxzM7Oslngn6AujJPg7Oxsu91+fHz0er1ggQ4PDxuNBllvwiZgKV5eXiA2vbJR8bPVarXXTl8ecaurq1j+WXkSTUokEjgAarXa119//fz8DJB5ZWWFEC03CI1GA92TpMbs7GwoFEJxi8ViBHMCgQC+tfn5eXqwIHfs7+9XKhWsgK1WK51Og9UhZsqxAtpguVymEQq1QiqVMtuTuEW3zmQyWKURFOr1OpAnj8cD0Y8NAP1ypVKJYxRSYLFYvL29ZZ1J7BuaBCBowPH4YslAPT4+QsBi5zU9PR2LxS4uLnDfLS8vk8cFdE7dB7RqrPe8fVLwd3d38Xh8bGysVqtZLBZYEAR/MMUCwWEhgIWyUCiIRCKr1ZpMJmk+QLeamZnZ3t5moSt2uVwul8vv93OSsdlsxAEo6RodHcV1h/zGgZ+DHw5RbOD9fr/ZbC4uLj48PPj9/kKhcHZ2Bs6QhH8mkyH5znOm2+1CNwAF0m63z87OqAsRBIEtlyAI1WpVJBLxhklecJnyfiQSycLCAnwk/MhYznm+YT0g23Z+fs76gx5NRD673Y6mfXJygqLGEtRgMHCOYOfCwtjtdkNMorh4aWnp+PiYLBZfbKQv0so7OzskBpk7COZotdrt7e1wOIyRuVKp2O32t2/fGo1GJp1Wq8UjjgsdfCmebg5HT09PAKxBWhAppHuVMNXj4+Pp6SnZwlAoZLfbA4EABnBqK1qtlsPhgGLPARsbOKdOipqz2ewrJCYSiTD4yGQyAPxASXjuMTSx6HU4HLFYTCaTvby8OByO2dlZ0h/Ly8scD9FQAQ7DEx8ZGXktguFbp1KpwIjg5iTfkE6nz87OxGIxLmGe7VzNrDCGw2GpVOJDazQau7u7dInzOAkEAmxnW61WOBxmQB4bG5udnYUnTjkjpDsOdNVqlcZCIgv39/fJZNLj8XC3ouqVfQp8bNbber3e6/VOTU25XC74uGym5ubmKOV53STAvgV1wbvo9Xqzs7NXV1e7u7sqlYqH5fX1tSAIh4eHt7e39Xr9NYTG/4St0+TkJBetSqXiz5PJIPMViUReQwwWi0Uul8fjca1WyyjU6XQIwoVCIavVyrLZ6/W63e65uTmNRnN5eUmgQaFQULDCzx8Oh8/Pz7+Io19eX15fXj/xS3x8fMwmleqD3d1djFKlUonhk+FNLBavra21Wi12qHK5nBpUYGfk2UBFdzodylZPTk7w6sFNFolErMfwSjF7W61W+ilotYWbCDI/lUoRfwQ+xtAEyFMulyOdNJtN8qO1Wg3HGntlMDMUIKGrBQIBtVpN1xHgz1wuB22FHB1B71wuJ5PJ6HPjqQsMFf1vOBzq9XrcUIFAgI6S8fHxnZ0dlvlobzxw6B9iE/7w8MCgCIVsOBy63W7qoOhPYekLYczn8x0eHnLIxB+JNYbKWKg5KpVqZWWFE3goFHK5XLhgZmdnodGwYoQZabfbx8bGgsEgv9ZisZjP57G9+Hy+arWaz+enp6cZsnArYSKCgcQCD/ssB0CqM56enjBxGAwGDvm1Wi2Xy11fXz88PFDyEIvFcAYKgoBVhCAslWK5XI6CC1xtNI3jauW0CNJqc3MTZtLU1FQymUwkErgnuU6w+bVaLabL5eVlULXUO9BCxGz48PCg0+lisRg/pEajEYlE6P2Tk5Mej4daP6vVCq1ycnKSFalYLE4kEuRoOXBBUYKcxGqf+QIbG8PO+Pg48H1cZ3x6CwsLXq8XJ6RcLm82m5eXlyMjI6wCGHOgECqVylfhHzv14uLiq20HqG2xWIxGo7CLoAGNj48zAK6urk5OTpISUiqVmDwJMTWbzXA4rNfrDw4OcJRls9lAILC4uIiERGODXC6HYNTr9TiepNPpt2/fDofDQCAgKhaLEE0FQfD5fFxqsBLI5w6Hw8PDQ5VKFQgEGArAExLv12q1CATD4ZA1OC2Qy8vLR0dHVBnAGcaXzRuIxWKPj4+YEVZXV8Vi8f/7f/+PawtuaCgUUiqVl5eX/J64wvDVMTBjyAHST1geZw70PSzPgUBgfHwcnojNZkPW4u28Gt7r9TosZe4O9XpdLpdTPPH09EQAj3ZiQPg0p2FnQMHe3t5mvms0GhaLhVsnVmi1Wn1xcQG4DM0Ps/b19TVfNrfbLZFIsD+Ew2HABzKZDAbkYDAAXU+gfmlpifMtJ38W+KAJ+v3+8vLy7u4uykUkEnG73eAhEC97vZ5CoaDiGEcMNT8wKTY2NhCMoYSOjY0plUr+FbqIwIvw3sH8CoLAHPTw8FAqlaampq6urt68eROPx2n/fnx8VCqVaExHR0cymYyDvUwmo2Dh8PBwfn7++Pg4FAqNjY2dn59Tc1cqlW5vb6njicViEolkamqq1+tRMU/qf2xsjNHs8fFRq9Xe3d1RYHp0dATgUvVPheT8ZxyoCNi1Wm16ehrmBRIgNpObm5v19XVER71ez6+4UCiQ6uz1eviJdDod1WXwMf1+P6M3mQZa7xBNuWjJeUokEp4oOzs7TOsIVff39wqFolKpDIdDRhhuATBxeQTa7fbPnz8vLCyAlSgUCre3t1tbW9vb2+vr67FYbDAYLCwsXF5e+v3+nZ0dhUKxvLyMnA+3TRAEHmAg79rtNs8VaH7EYWu12uTk5NHREffu+fl5+JJ0enMskMvltK85HI7/D7Elbh1ph6gcAAAAAElFTkSuQmCC');">
                                                                <div id="captcha_spinner" style="position:absolute;
                                                                            width: 1.1em;
                                                                            height: 1.1em;
                                                                            right:0.2em;
                                                                            top:0.2em;
                                                                            background-size: contain;
                                                                            background-image: url(/static/img/captcha-refresh.svg);">

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>


                                                <form>
                                                    <div class="mt-3 d-flex flex-row"
                                                         style="width: 15em;display: block;">
                                                        <div>
                                                            <button id="receive" class="btn btn-block btn-primary"
                                                                    name="receive">
                                                                Get test coins!
                                                            </button>
                                                        </div>
                                                        <div class="ml-2">

                                                        </div>

                                                    </div>
                                                </form>
                                            </div>
                                            <input type="hidden" id="captcha_signature"
                                                   value="e101e0e1557ce9f23e60cca4c663920ba9d56d2f">
                                            <input type="hidden" id="captcha_salt" value="c0603158a77e5427cfcee3f30776d26a">
                                            <input type="hidden" id="captcha_time" value="2912995">


                                            <script>onload();</script>

                                        </div>
                                        <div id="tx_loading"
                                             class="  logo  mr-2 mt-4 f_spin align-top smaller"
                                             style="display: none;"></div>

                                        <div id="faucet-result"
                                             class=" mt-4 "
                                             style="display: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <a
                                    class="arrow-container hide-dashboard
                                        
                                     show-dashboard
                             ">
                                <div class="arrowx"></div>
                                <div class="arrowx"></div>
                                <div class="arrowx"></div>
                            </a>
                        </div>
                    </div>
                
            </div>
        </div>
        <nav id="sticky" class="header header-sticky sticky-top search-container flex-column">
            <div class="container-fluid col-xl-11 " role="navigation">
                <div class="row justify-content-between align-items-center">
                    <div class="col logo-wrap ">
                        <a href="/">
                            <div class=" navbar-brand logo  slow_spin align-top"></div>
                        </a>
                    </div>
                    <div class="col search-box-wrap">
                        <div class="  ">
                            <div class="pr-3" style="display:flex;position:relative;">
                                <input type="text" id="search-box" class=" input-box"
                                       autocomplete="off" autocapitalize="off" spellcheck="false"
                                       placeholder="Search in blockchain for block, transaction or address ..."
                                       onkeydown="check_pointer(this);" onkeyup="check_pointer(this)"
                                       onpaste="check_pointer(this)">
                                <span class="fa fa-search   search_icon"
                                      onclick="window.location.href='/'+document.getElementById('search-box').value;">
                                    </span>

                            </div>
                        </div>
                    </div>
                    <div class="scan-qr" onclick="searchBarScanQrCode();">
                    </div>
                    <div class="col menu-picto-wrap moon">
                        <i class="fa fa-lg theme ml-1"></i>
                    </div>
                    <div class="col menu-picto-wrap">
                        <div id="rmenu" data-toggle="collapse" data-target="#collapse-menu" role="navigation"
                             aria-expanded="false" aria-controls="collapse-menu"
                             class="mobilemenu-toggle d-flex flex-column justify-content-around collapsed  ">
                            <div class="icon-bar top-bar"></div>
                            <div class="icon-bar middle-bar"></div>
                            <div class="icon-bar bottom-bar"></div>
                        </div>
                    </div>
                </div>


            </div>
            <div class="collapse " id="collapse-menu">
                <div class=" pt-1 pl-4 pr-4 pb-4">
                    <div class="row" style="cursor: default;">
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-6   pt-4 pl-4">
                            <div class="inbox-item m-b-10 pb-2">Blockchain</div>
                            <div class="inbox-item m-l-10"><a class=""
                                                              href="/blocks">Blocks</a>
                            </div>
                            
                            <div class="inbox-item m-l-10"><a class=""
                                                              href="/statistics">Statistic</a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6  pt-4 pl-4">
                            <div class="inbox-item m-b-10 pb-2">Developer</div>
                            <div class="inbox-item m-l-10"><a class=""
                                                              href="https://developer.bitaps.com">Developer portal</a>
                            </div>
                            <div class="inbox-item m-l-10"><a class=""
                                                              href="https://developer.bitaps.com/blockchain">Blockchain API</a>
                            </div>
                            <div class="inbox-item m-l-10"><a class=""
                                                              href="https://developer.bitaps.com/market">Market API</a>
                            </div>
                            <div class="inbox-item m-l-10"><a class=""
                                                              href="https://developer.bitaps.com/native">Native API</a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6   pt-4 pl-4">
                            <div class="inbox-item m-b-10 pb-2">Tools</div>
                            <div class="inbox-item m-l-10"><a class="" href="/bip32">BIP32 calculator</a></div>
                            <div class="inbox-item m-l-10"><a class="" href="/mnemonic">Mnemonic</a></div>
                            <div class="inbox-item m-l-10">
                                <a class="" href="/broadcast">Broadcast transaction</a>
                            </div>

                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6   pt-4 pl-4">
                            <div class="inbox-item m-b-10 pb-2">Accept bitcoin</div>
                            
                            <div class="inbox-item m-l-10"><a class=""
                                                              href="https://developer.bitaps.com/forwarding">Payment forwarding</a>
                            </div>
                            
                            

                        </div>

                    </div>
                    <div class="row   " style="cursor: default;">
                        <div class="col-lg-3 col-md-2 col-sm-4 col-xs-6   pt-4 pl-4">
                            <div class="inbox-item m-b-10 pb-2">Network</div>
                            <div class="inbox-item m-l-10"><a
                                    href="http://btc.bitaps.com">Bitcoin</a></div>
                            <div class="inbox-item m-l-10"><a href="http://ltc.bitaps.com">Litecoin</a></div>
<!--                            <div class="inbox-item m-l-10"><a href="http://eth.bitaps.com">Ethereum </a></div>-->

                            <div class="inbox-item m-l-10"><a
                                    href="http://tbtc.bitaps.com">Bitcoin testnet</a>
                            </div>
                            <div class="inbox-item m-l-10"><a href="http://tltc.bitaps.com">Litecoin testnet</a></div>
<!--                            <div class="inbox-item m-l-10"><a href="http://teth.bitaps.com">Ethereum testnet</a></div>-->
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6   pt-4 pl-4">
                            <div class="inbox-item m-b-10 pb-2">Learn more</div>
                            <div class="inbox-item m-l-10"><a class=""
                                                              href="/bitcoin">About bitcoin</a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6   pt-4 pl-4">
                            <div class="inbox-item m-b-10 pb-2">Company</div>
                            <div class="inbox-item m-l-10"><a class=""
                                                              href="/vision">Our vision</a>
                            </div>
                            <div class="inbox-item m-l-10"><a class=""
                                                              href="https://medium.com/@bitaps.com">Blog</a>
                            </div>
                            <div class="inbox-item m-l-10">
                                <a class="" href="/privacy">Privacy policy</a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-2 col-sm-3 col-xs-6  pt-4 pl-4">
                            <div class="inbox-item m-b-10 pb-2">Language</div>

                            
                                <div class="inbox-item m-l-10"><a onclick="return change_language('en');"
                                                                  href="https://tbtc.bitaps.com/mnemonic/challenge?language=en">English(US)</a>
                                </div>
                                <div class="inbox-item m-l-10"><a onclick="return change_language('ru');"
                                                                  href="https://tbtc.bitaps.com/mnemonic/challenge?language=ru">Русский(RU)</a>
                                </div>
                            

                        </div>
                    </div>
                </div>
            </div>
            
            
            
            
            
            
            
            
            
            
            
            
        </nav>

        <div id="search-bar-qr-scanner" class="" style="display: none;">
            <div id="search-bar-camera-loading-message" class="mt-4 text-center w-100 p-name ">
                🎥 Unable to access video stream (please make sure you have a webcam enabled)
            </div>
            <div class="d-flex justify-content-center mt-4 ">
                <div class="position-relative pb-2">
                    <canvas id="search-bar-canvas" hidden style="width: 30em;max-width: 100%!important;"></canvas>
                    <div id="search-bar-camera-close" class="x-btn position-absolute"
                         onclick="searchBarCloseCamera();" hidden style="right: 0.3em;top:0.7em;"></div>
                </div>
            </div>
        </div>
    
    
    

    


    <div class="text-block" >


        <div class="container-fluid col-xl-10  mt-4 mb-4  pb-4   d-flex flex-column h-100 w-100" style="flex: auto;max-width: 900px;">

                        <div class="d-flex flex-row justify-content-center m-4">
                <div class="brute-logo"
                     style="background-image: url(/static/img/brute.jpg);height: 14em;width: 23em;
                            background-size: cover;"></div>
            </div>


            <h4 class="text-center mb-4">1 BTC challenge with splitted mnemonic code</h4>
            <br>
            <div style="font-size: 1.3em;">
            <div class="d-flex justify-content-start">
                <div class="mr-4">Address: </div>
                <div><a class="bold" style="font-family: 'Roboto', sans-serif;word-break: break-all;" href="/******************************************">******************************************</a>  1<span class="amount-tail-zerro">.</span><span class="after-dot"><span class="amount-tail-zerro">00000000</span></span><span class="font-bd">tBTC</span></div>
            </div>
            <div class="d-flex mt-4" style="overflow:hidden;">
                   <div class="mr-4">Zpub: </div>
                  <div class="bold " style="font-family: 'Roboto', sans-serif;color: #009688!important; word-break: break-all;">zpub6qdEDkv51FpxX6g1rpFGckmiL46vV8ccmtEgPAkj3qj8N4ZZHyXDRA9RwpTiFK2Kb8vRaDmSmwgX6rfB4t2K8Ktdq8ExQ6fumKpn2ndJCqL</div>

               </div>
            </div>
              <br>
            <p>
                The 12-word original mnemonic code was split using the <a href="https://en.wikipedia.org/wiki/Shamir%27s_Secret_Sharing">Shamir Secret Sharing</a> scheme with 3 out of 5 threshold schemes were used.
                This means that any three shares are sufficient to restore the original mnemonic code.
                The goal is to break the Shamir Secret Sharing scheme or break the implementation of software for SSSS. We publish 2 of 3 shares needed to restore the original mnemonics. </p>

           <div class="d-flex flex-column justify-content-center">
               <div class="mt-4">Share 1:</div>
               <div id="share-input-1"  style="flex-wrap: wrap;" class="mnemonic-code textarea share-div  words-count-ok d-flex  justify-content-center mt-2" maxlength="216"  ><span class="valid word-1">session</span><span class="other"> </span><span class="valid word-2">cigar</span><span class="other"> </span><span class="valid word-3">grape</span><span class="other"> </span><span class="valid word-4">merry</span><span class="other"> </span><span class="valid word-5">useful</span><span class="other"> </span><span class="valid word-6">churn</span><span class="other"> </span><span class="valid word-7">fatal</span><span class="other"> </span><span class="valid word-8">thought</span><span class="other"> </span><span class="valid word-9">very</span><span class="other"> </span><span class="valid word-10">any</span><span class="other"> </span><span class="valid word-11">arm</span><span class="other"> </span><span class="valid word-12">unaware</span></div>
               <div class="mt-4">Share 2:</div>
               <div id="share-input-2"  style="flex-wrap: wrap;"  class="mnemonic-code textarea share-div   words-count-ok d-flex justify-content-center mt-2" maxlength="216" ><span class="valid word-1">clock</span><span class="other"> </span><span class="valid word-2">fresh</span><span class="other"> </span><span class="valid word-3">security</span><span class="other"> </span><span class="valid word-4">field</span><span class="other"> </span><span class="valid word-5">caution</span><span class="other"> </span><span class="valid word-6">effort</span><span class="other"> </span><span class="valid word-7">gorilla</span><span class="other"> </span><span class="valid word-8">speed</span><span class="other"> </span><span class="valid word-9">plastic</span><span class="other"> </span><span class="valid word-10">common</span><span class="other"> </span><span class="valid word-11">tomato</span><span class="other"> </span><span class="valid word-12">echo</span></div>
           </div>
            <br>
            <p>
                In case of success your 1 BTC will waiting for you at <span style="color: #009688;">m/84'/0'/0'/0/0</span> path.
                We use this <a href="/mnemonic">mnemonic tool</a> to split code.<p>
            <p class="text-left">
                Details about used implementation of SSSS algorithm you can find here:
            <br><a href="https://github.com/bitaps-com/mnemonic-offline-tool/blob/master/BIP/mnemonic-improvement.md" style="word-break: break-all;">https://github.com/bitaps-com/mnemonic-offline-tool/blob/master/BIP/mnemonic-improvement.md</a>
        </p>
            <p class="text-left">
                Exact software implementation you can find here:
                <br><a href="https://github.com/bitaps-com/jsbtc/blob/master/src/functions/shamir_secret_sharing.js#L88" style="word-break: break-all;">https://github.com/bitaps-com/jsbtc/blob/master/src/functions/shamir_secret_sharing.js#L88</a>
            </p>


            <p>Good luck :)</p>




        </div>


    </div>










</div>

    <footer id="footer" class=" footer  ">
        <div class=" container-fluid lattolight pt-1 pl-4 pr-4 pb-4">
            <div class="row " style="cursor: default;">
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3  pt-4 pl-4">
                    <div class="inbox-item m-b-10 pb-2">bitaps.com</div>
                    <div class="inbox-item m-l-10"><a
                            href="/vision">Our vision</a>
                    </div>
                    <div class="inbox-item m-l-10"><a
                            href="https://medium.com/@bitaps.com">Blog</a>
                    </div>
                    <div class="inbox-item m-l-10"><a
                            href="/privacy">Privacy policy</a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3   pt-4 pl-4">
                    <div class="inbox-item m-b-10 pb-2">Services</div>
                    <div class="inbox-item m-l-10"><a
                            href="https://developer.bitaps.com/forwarding">Accept payments</a>
                    </div>
                    <div class="inbox-item m-l-10"><a
                            href="/">Block explorer</a></div>


                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3   pt-4 pl-4">
                    <div class="inbox-item m-b-10 pb-2">Language</div>

                    
                    
                        <div>
                            <a onclick="return change_language('en');"
                               href="https://tbtc.bitaps.com/mnemonic/challenge?language=en">English(US)</a>
                        </div>
                        <div>
                            <a onclick="return change_language('ru');"
                               href="https://tbtc.bitaps.com/mnemonic/challenge?language=ru">Русский(RU)</a>
                        </div>
                    


                </div>


                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3   pt-4 pl-4">
                    <div class="inbox-item m-b-10 pb-2">Social</div>
                    <div class="inbox-item m-l-10"><a href="https://twitter.com/bitaps_com">Twitter</a></div>
                    <div class="inbox-item m-l-10"><a href="https://t.me/bitapscom">Telegram</a>
                    </div>

                </div>

            </div>
            <div>
                <div class="d-flex flex-row pt-3  pb-2 justify-content-end align-items-end  ">
                    <div style="color: white;font-size: 0.8em;"> Powered by</div>
                    <a href="https://github.com/bitaps-com/jsbtc"><span class="powered jsbtc"></span></a>
                    <a href="https://pybtc.readthedocs.io"><span class="powered pybtc"></span></a>
                    <a href="https://github.com/bitaps-com/btcapiserver"><span class="powered btcapi"></span></a>
                    <a href="https://postgrespro.com"><span class="powered postgress"></span></a>

                </div>
            </div>
            <hr class="mt-1 mb-1" style="border-color: var(--footer-line-color);">

            <small style="font-size: 0.9em;color:white;"> © 2014 - 2020 bitaps.com</small>

        </div>
    </footer>

    <div onclick="topFunction()" id="upBtn" class="upBtn-wrap">
        <div class="arrow-container upBtn">
            <div class="arrowx"></div>
            <div class="arrowx"></div>
            <div class="arrowx"></div>
        </div>
    </div>




<script src="/static/js/jquery-3.3.1.min.js?1742507143360"></script>

<script src="/static/js/poper.js?1742507143360"></script>

<script src="/static/js/bootstrap.min.js?1742507143360" crossorigin="anonymous"></script>

<script src="/static/js/tether.min.js?1742507143360"
        integrity="sha384-DztdAPBWPRXSA/3eYEEUWrWCy7G5KFbe8fFjk5JAIxUYHKkDx6Qin1DkWx51bBrb"
        crossorigin="anonymous"></script>


<script>
    "use strict";

    function set_cookie(name, value, exp_y, exp_m, exp_d, path, domain, secure) {
        var cookie_string = name + "=" + escape(value);
        if (exp_y) {
            var expires = new Date(exp_y, exp_m, exp_d);
            cookie_string += "; expires=" + expires.toGMTString();
        }
        if (path)
            cookie_string += "; path=" + escape(path);
        if (domain)
            cookie_string += "; domain=" + escape(domain);
        if (secure)
            cookie_string += "; secure";
        document.cookie = cookie_string;
    }

    // fix input scroll on chrome for sticky header
    $('#search-box').on('input ', function () {

        var p = $('<div style="font-size:1.2em"></div>').attr('id', 'tmp_search2')
            .html(this.value.replace(/[&\/\\#,+()$~%.'":*?<>{}]/g, 'A') + 'AAAA')
            .css({
                'display': 'inline'
            })
            .appendTo('#sticky');
        if (p[0].innerHTML.length > 4)
            $(this).css({'max-width': (p[0].offsetWidth) + 'px'});
        else
            $(this).css({'max-width': '30rem'});
        p.remove();
        var wpe = $(this).data("wpe");
        if (wpe == undefined) wpe = false;
        if (!wpe) {
            $(this).data("wpy", window.pageYOffset);
            $(this).data("wpx", window.pageXOffset);
            $(this).data("wpe", true);
        }
    });

    $(window).on('scroll', function () {

        var el = $(document.activeElement);
        if (el[0].id == "search-box") {
            var wpy = $(el[0]).data("wpy");
            var wpx = $(el[0]).data("wpx");
            var wpe = $(el[0]).data("wpe");
            if (wpe == true) {
                if (wpy != window.pageYOffset) {
                    window.scrollTo(wpx, wpy);
                    $(el[0]).data("wpe", false);
                }
            }
        }
        var sticky = $('#sticky');
        var offset = $('#header-top').outerHeight();
        var htop = $('#header-top');
        if (offset < $(document).scrollTop()) {
            
        } else {
            sticky.css({position: "sticky", top: 0});
            
        }

        $('#rmenu').on('click touch', function () {
            var offset = $('#header-top').outerHeight();
            var sticky = $('#sticky');
            var apr = sticky.data("apr");
            if (offset >= $(document).scrollTop() + 1) {

                if (wpe != true) {
                    sticky.data("apr", true);

                    var body = $("html, body");
                    body.stop().animate({scrollTop: offset}, 500, 'linear', function () {
                        sticky.data("apr", false);

                    });

                }
            }

        });
    })
</script>


<script>

    var timestamp = 1747797357507;
    var update_options = [];
    var last_update = {};
    var content_update = undefined;

    function close_warning(warning, obj) {
        let d = $(obj.parentElement.parentElement.parentElement);
        d.hide();
        cookies.set("testnet_warning_close", "1");


    }


    function switch_theme() {
        let a = $(document.documentElement).attr('data-theme');
        if (a === 'dark') {
            $(document.documentElement).attr("data-theme", "light");
            cookies.set("theme", "light", {path: '/', domain: '.bitaps.com'});
            try {
                theme_light();
            } catch (e) {
            }

            $('#theme-color').attr('content', "#f1f1f1");
            $('#msapplication-navbutton-color').attr('content', "#f1f1f1");
            $('#apple-mobile-web-app-status-bar-style').attr('content', "#f1f1f1");
        } else {
            $(document.documentElement).attr("data-theme", "dark");
            cookies.set("theme", "dark", {path: '/', domain: '.bitaps.com'});
            try {
                theme_dark();
            } catch (e) {
            }
            $('#theme-color').attr('content', "#10171e");
            $('#msapplication-navbutton-color').attr('content', "#10171e");
            $('#apple-mobile-web-app-status-bar-style').attr('content', "#10171e");
        }

    }

    function hide_dashboard() {
        if (dsh_bussy) return;
        dsh_bussy = 1;
        if (dsh_status === 1) {
            dsh_status = 0;
            cookies.set(dsh_cookie, "0");
            $("#net-dashboard").animate({height: "0px"}, {
                easing: 'swing',
                duration: 200,
                complete: function () {
                    $('body').scrollTop(0);
                    $('html').scrollTop(0);
                    $(".top-stat").show();
                    $(".hide-dashboard").addClass("show-dashboard");
                    dsh_bussy = 0;
                }
            });
        } else {
            dsh_status = 1;
            cookies.set(dsh_cookie, "1");
            let d = $("#net-dashboard");
            let auto = d.css('height', 'auto').height();
            d.height("0em").animate({height: auto}, {
                easing: 'swing',
                duration: 200,
                complete: function () {
                    $('body').scrollTop(0);
                    $('html').scrollTop(0);
                    $("#net-dashboard").height("auto");
                    $(".top-stat").hide();
                    $(".hide-dashboard").removeClass("show-dashboard");
                    dsh_bussy = 0;

                }
            });


        }

    }


    $('.hide-dashboard').on('click tap', function () {
        hide_dashboard()
    });
    $('.moon').on('click tap', function () {
        switch_theme()
    });
    var dsh_status =  0 ;
    var dsh_bussy = 0;
    var dsh_cookie =  "hide_dashboard" ;

    $('#collapse-menu').on('show.bs.collapse', function () {

        $('#collapse-menu').css('max-height', $(window).height() + 20 + "px");

        window.onresize = function () {
            $('#collapse-menu').css('max-height', $(window).height() + 20 + "px");
            console.log($('#collapse-menu').first().height());
            if ($('#collapse-menu').first().height() >= $(window).height() - 90) {
                $('#collapse-menu').css('overflow-y', "scroll");
            } else {
                $('#collapse-menu').css('overflow-y', "hidden");
            }
        }

    });

    $('#collapse-menu').on('shown.bs.collapse', function () {
        if ($('#collapse-menu').height() >= $(window).height()) {
            $('#collapse-menu').css('overflow-y', "scroll");
        } else {
            $('#collapse-menu').css('overflow-y', "hidden");
        }

    });

    $('#collapse-menu').on('hidden.bs.collapse', function () {
        window.onresize = function () {
        };
    });

    $("#faucet-amount").on('focus', function () {
        $("#faucet-amount")[0].style.color = "";
    });


    $(document).ready(function () {
        $('input[type="number"]').on('keyup', function () {
            v = parseInt($(this).val());
            min = parseInt($(this).attr('min'));
            max = parseInt($(this).attr('max'));

            if (v < min) {
                $(this).val(min);
            } else if (v > max) {
                $(this).val(max);
            }
        })
    });
    var captcha_mutex = false;

    async function refresh_captcha() {
        if (!captcha_mutex) {
            captcha_mutex = true;
            $("#captcha_spinner").addClass("fast_spin");
            try {
                let v = await curl('https://tbtc.bitaps.com/js/refresh/captcha');
                $("#captcha-img").css('background-image', 'url(data:image/png;base64,' + v.captcha + ')');
                $("#captcha_salt")[0].value = v.captcha_salt;
                $("#captcha_signature")[0].value = v.captcha_signature;
                $("#captcha_time")[0].value = v.captcha_time;


            }
            catch (e) {
            }
            finally {
                $("#captcha_spinner").removeClass("fast_spin");
                captcha_mutex = false;
            }

        }


    }

    async function async_faucet(address, amount, captcha,
                                captcha_signature, captcha_salt, captcha_time) {
        try {
            let data = {"address": address,
                        "amount": amount,
                        "captcha": captcha,
                        "captcha_signature": captcha_signature,
                        "captcha_salt": captcha_salt,
                        "captcha_time": captcha_time,
            };
            let url = "https://tbtc.bitaps.com/js/faucet";
            let v = await curl(url, "POST", JSON.stringify(data));
            if (v !== undefined) {
                if ("tx_list" in v) {
                    let t = "<div class=\"d-flex flex-column smaller\">";
                    let b = "";
                    let h = v["tx_list"][0]["tx_hash"];
                    $("#faucet-result")[0].innerHTML = "<a href=\"/" + h + "/" + address + "\">" + h + "</a>";
                    if ("dbs" in v) {
                        let h1 = v["tx_list"][0]["tx_hash"];
                        let h2 = v["dbs"][0]["tx_hash"];
                        b += "<a href=\"/" + h1 + "/" + address + "\" style=\"color:red !important;\">" + h1 + "</a>";
                        b += "<a href=\"/" + h2 + "/" + address + "\" >" + h2 + "</a>";
                    } else {
                        let h = v["tx_list"][0]["tx_hash"];
                        b += "<a href=\"/" + h + "/" + address + "\" >" + h + "</a>";
                    }

                    $("#faucet-result")[0].innerHTML = t + b + "</div>";


                    $("#faucet-result").show();
                } else {
                    $("#faucet-result")[0].innerHTML = v.error_message;
                    $("#faucet-result").show();
                    $("#faucet-result").addClass("text-red");
                }

            }


        } catch (e) {
            let m = "Request failed";
            if ("status" in e) {
                if (e["status"] == 429) {
                    m = "Requests limit is over";
                    try {
                        r = e.responseJSON;
                        let ip = r.ip;
                        let seconds = parseInt(r.ratelimit_reset);
                        m = "<span style=\"color:yellow\"> IP: " + ip + " - " +
                            seconds + " seconds left to limit reset </span>";
                    } catch (e) {
                    }
                }
            }
            $("#faucet-result")[0].innerHTML = m;
            $("#faucet-result").show();

        } finally {
            $(".tx_loading").hide();
            $("#receive").prop('disabled', false);
            $("#dbs").prop('disabled', false);
        }
    }

    window.onscroll = function () {
        scrollFunction()
    };

    function scrollFunction() {
        if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
            document.getElementById("upBtn").style.display = "block";
        } else {
            document.getElementById("upBtn").style.display = "none";
        }
    }

    // When the user clicks on the button, scroll to the top of the document
    function topFunction() {
        $('html,body').animate({scrollTop: 0}, 'slow');
    }


    $.getScript("/static/js/jsQR.js?1742507143360", function () {
        $(".scan-qr").show();
        qrScannerLoaded = true;
    });

    var qrScannerLoaded = false;

    function drawLine(canvas, begin, end, color) {
        canvas.beginPath();
        canvas.moveTo(begin.x, begin.y);
        canvas.lineTo(end.x, end.y);
        canvas.lineWidth = 4;
        canvas.strokeStyle = color;
        canvas.stroke();
    }


    var searchBarCameraActive = false;
    var searchBarVideo = document.createElement("video");
    var searchBarCanvasElement = document.getElementById("search-bar-canvas");
    var searchBarCanvas = searchBarCanvasElement.getContext("2d");
    var searchBarLoadingMessage = document.getElementById("search-bar-camera-loading-message");
    var searchBarCameraClose = document.getElementById("search-bar-camera-close");

    function stick() {
        if (!searchBarCameraActive) {

            searchBarCameraClose.hidden = true;
            searchBarVideo.pause();
            searchBarVideo.src = "";
            searchBarVideo.srcObject.getTracks()[0].stop();
            searchBarCanvasElement.hidden = true;
            $("#search-bar-qr-scanner").hide();
            return;
        }
        searchBarLoadingMessage.innerText = "Loading video...";
        if (searchBarVideo.readyState === searchBarVideo.HAVE_ENOUGH_DATA) {
            searchBarCameraClose.hidden = false;

            searchBarLoadingMessage.hidden = true;
            searchBarCanvasElement.hidden = false;

            searchBarCanvasElement.height = searchBarVideo.videoHeight;
            searchBarCanvasElement.width = searchBarVideo.videoWidth;
            searchBarCanvas.drawImage(searchBarVideo, 0, 0, searchBarCanvasElement.width, searchBarCanvasElement.height);
            var imageData = searchBarCanvas.getImageData(0, 0, searchBarCanvasElement.width, searchBarCanvasElement.height);
            var code = jsQR(imageData.data, imageData.width, imageData.height, {
                inversionAttempts: "dontInvert",
            });
            if ((code) && (code.data.length > 0)) {

                drawLine(searchBarCanvas, code.location.topLeftCorner, code.location.topRightCorner, "#FF3B58");
                drawLine(searchBarCanvas, code.location.topRightCorner, code.location.bottomRightCorner, "#FF3B58");
                drawLine(searchBarCanvas, code.location.bottomRightCorner, code.location.bottomLeftCorner, "#FF3B58");
                drawLine(searchBarCanvas, code.location.bottomLeftCorner, code.location.topLeftCorner, "#FF3B58");
                $("#search-box")[0].value = code.data;
                if (check_pointer($("#search-box")[0]))
                    window.location.href = '/' + document.getElementById('search-box').value;

                searchBarCameraActive = false;
            }
        }
        requestAnimationFrame(stick);
    }


    function searchBarScanQrCode() {
        $("#search-bar-qr-scanner").show();
        searchBarCameraActive = true;
        navigator.mediaDevices.getUserMedia({video: {facingMode: "environment"}}).then(function (stream) {
            searchBarVideo.srcObject = stream;
            searchBarVideo.setAttribute("playsinline", true);
            searchBarVideo.play();
            requestAnimationFrame(stick);
        });
    }

    function searchBarCloseCamera() {
        searchBarCameraActive = false;
    }

</script>


<script src="/static/js/lib.js?1742507143360"></script>



<script src="/static/js/core.js?1742507143360"></script>
</body>
</html>