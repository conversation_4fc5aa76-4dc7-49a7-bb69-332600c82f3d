{"name": "shamirs-secret-sharing", "version": "2.0.0", "description": "A simple implementation of <PERSON><PERSON><PERSON>'s Secret Sharing configured to use a finite field in GF(2^8) with 128 bit padding", "main": "index.js", "type": "module", "exports": {"import": "./index.js", "require": "./module.cjs"}, "directories": {"test": "test"}, "scripts": {"test": "npm run test:node", "test:node": "cd test && node --test --test-reporter tap runtimes/node.js", "test:socket": "cd test && ssc build --headless -r -o", "generate:commonjs": "rm -f module.cjs && esbuild index.js --bundle --format=cjs > module.cjs"}, "keywords": ["shamir", "secret", "sharing", "sss"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/jwerle/shamirs-secret-sharing.git"}, "bugs": {"url": "https://github.com/jwerle/shamirs-secret-sharing/issues"}, "homepage": "https://github.com/jwerle/shamirs-secret-sharing#readme"}