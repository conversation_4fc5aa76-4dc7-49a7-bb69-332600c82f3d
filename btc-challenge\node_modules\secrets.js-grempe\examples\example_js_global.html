<!-- USAGE : Just open this file in your web browser of choice -->

<html>
  <head>
    <title>secrets.js - HTML w/ JS Global Test</title>
  </head>

  <body>
    <h1>secrets.js</h1>
    <p>
      You can also open a javascript console to interact directly with the
      'secrets' global.
    </p>

    <br />

    <h2>Code Output:</h2>
    <p>
      You should see three identical keys below, with a key both before and
      after a share and combine operation.
    </p>
    <div id="output"></div>

    <br />

    <script src="../node_modules/jquery/dist/jquery.min.js"></script>
    <script src="../secrets.js"></script>

    <script charset="utf-8">
      var key, comb1, comb2, shares, newShare

      secrets.init()
      key = secrets.random(512)
      shares = secrets.share(key, 10, 5)
      comb1 = secrets.combine(shares)
      newShare = secrets.newShare(8, shares)
      comb2 = secrets.combine(shares.slice(1, 5).concat(newShare))
      $("#output").text(key + "\n" + comb1 + "\n" + comb2)
    </script>
  </body>
</html>
