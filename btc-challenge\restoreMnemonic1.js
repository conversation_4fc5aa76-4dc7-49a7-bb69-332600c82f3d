const secrets = require('secrets.js-grempe');

// Shares gerados (substitua pelos shares gerados pelo script anterior)
const share1 = '80101c69a3415c8b31d7dc190ce85b165be200ee5883b3982f8819d8158f7d4841f67cca880e3932f00969b8dc9e02ab1bf3ac19c07df6daaf10906bd1d11c3b5033ea2e80a7012dc1f6b6d6c75b33401d73aec7eed2efe0a5fc2a04a22ced7f8c0ed3238e0b84fc055dc48169fc57f01dbdb95e11051c19c5cf2be8fe1fb176a5a2fcb1ab3fbf0de9bbeac2c195b04dfdd45036778de2630299ed9115926f54721';
const share2 = '802029129682a8d7b3afa9f3d81177ccace408ad7ae76eb194e1f951f2ff3c11589ce134dbedb4f5e8b3199072cdd207fe8740925a1a340498b12906782223277997ce8cdace0b8a54ad66ed8497bf40210746ffcb35c53142899c294fc8116ede9c7d070426d349d05a5052cbc978a0220aba1dfb6a230250ff9150370ebbed4175e1d34d2eb89a1bd61e6589bb6b3a3028a72ce42a1fd60e9213a22274c588ed7';

// Combine os shares para restaurar a frase mnemônica
try {
    const restoredMnemonic = secrets.hex2str(secrets.combine([share1, share2]));
    console.log('Frase mnemônica restaurada:', restoredMnemonic);
} catch (error) {
    console.error('Erro ao restaurar a frase mnemônica:', error.message);
}