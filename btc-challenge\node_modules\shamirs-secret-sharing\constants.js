export const PRIMITIVE_POLYNOMIAL = 29

export const BIT_PADDING = 128
export const BIT_COUNT = 8
export const BIT_SIZE = 2 ** BIT_COUNT

export const BYTES_PER_CHARACTER = 2
export const MAX_BYTES_PER_CHARACTER = 6

export const MAX_SHARES = BIT_SIZE - 1

export const UTF8_ENCODING = 'utf8'
export const BIN_ENCODING = 'binary'
export const HEX_ENCODING = 'hex'

export default {
  PRIMITIVE_POLYNOMIAL,

  BIT_PADDING,
  BIT_COUNT,
  BIT_SIZE,

  MAX_SHARES,

  MAX_BYTES_PER_CHARACTER,
  BYTES_PER_CHARACTER,

  UTF8_ENCODING,
  BIN_ENCODING,
  HEX_ENCODING,
}
