const secrets = require('secrets.js-grempe');

// Frase mnemônica original
const mnemonic = 'session,cigar,grape,merry,useful,churn,fatal,thought,very,any,arm,unaware';

// Converte a frase mnemônica para hexadecimal
const hexMnemonic = secrets.str2hex(mnemonic);

// Gera 2 shares, sendo necessário 2 shares para restaurar a frase mnemônica
const shares = secrets.share(hexMnemonic, 2, 2);

// Exibe os shares gerados
console.log('Shares gerados:', shares);